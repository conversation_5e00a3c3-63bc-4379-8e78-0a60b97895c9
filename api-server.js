const express = require('express');
const match = require('./src/provider/match.js');

const app = express();
app.use(express.json());

// 设置CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Content-Type');
  next();
});

// 音源匹配API
app.get('/api/match/:id', async (req, res) => {
  try {
    const songId = parseInt(req.params.id);
    const sources = req.query.sources ? req.query.sources.split(',') : undefined;
    
    console.log(`查找歌曲 ${songId}，音源：${sources || '默认'}`);
    
    const result = await match(songId, sources);
    
    res.json({
      success: true,
      data: {
        id: songId,
        url: result.url,
        size: result.size,
        br: result.br,
        md5: result.md5,
        source: result.source
      }
    });
  } catch (error) {
    console.error('匹配失败:', error.message);
    res.status(404).json({
      success: false,
      error: '未找到匹配的音源',
      message: error.message
    });
  }
});

// 批量匹配API
app.post('/api/match/batch', async (req, res) => {
  try {
    const { ids, sources } = req.body;
    const results = [];
    
    for (const id of ids) {
      try {
        const result = await match(parseInt(id), sources);
        results.push({
          id: parseInt(id),
          success: true,
          data: result
        });
      } catch (error) {
        results.push({
          id: parseInt(id),
          success: false,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      results: results
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    availableSources: ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'youtubedl', 'ytdlp', 'bilibili', 'bilivideo', 'pyncmd']
  });
});

// 获取支持的音源列表
app.get('/api/sources', (req, res) => {
  const sources = require('./src/consts.js');
  res.json({
    success: true,
    defaultSources: sources.DEFAULT_SOURCE,
    allSources: Object.keys(sources.PROVIDERS)
  });
});

const PORT = process.env.API_PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 UnblockNeteaseMusic API Server 运行在端口 ${PORT}`);
  console.log(`📖 API 文档:`);
  console.log(`   GET  /api/health                    - 健康检查`);
  console.log(`   GET  /api/sources                   - 获取音源列表`);
  console.log(`   GET  /api/match/:id                 - 单曲匹配`);
  console.log(`   POST /api/match/batch               - 批量匹配`);
  console.log(`\n💡 使用示例:`);
  console.log(`   curl http://localhost:${PORT}/api/match/418602084`);
  console.log(`   curl http://localhost:${PORT}/api/match/418602084?sources=qq,kugou`);
});
