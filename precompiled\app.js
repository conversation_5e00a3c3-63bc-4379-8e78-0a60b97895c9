#!/usr/bin/env node
(()=>{var e={34:(e,t,r)=>{"use strict";var i=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:i(e)}},61:e=>{"use strict";e.exports=function(e){return e}},67:(e,t,r)=>{"use strict";r(7145)},81:(e,t,r)=>{"use strict";var i=r(9565),o=r(9306),s=r(8551),a=r(6823),c=r(851),u=TypeError;e.exports=function(e,t){var r=arguments.length<2?c(e):t;if(o(r))return s(i(r,e));throw new u(a(e)+" is not iterable")}},113:e=>{"use strict";let t={};function r(e,r,i){i||(i=Error);class o extends i{constructor(e,t,i){super("string"==typeof r?r:r(e,t,i))}}o.prototype.name=i.name,o.prototype.code=e,t[e]=o}function i(e,t){if(!Array.isArray(e))return`of ${t} ${String(e)}`;{let r=e.length;return(e=e.map(e=>String(e)),r>2)?`one of ${t} ${e.slice(0,r-1).join(", ")}, or `+e[r-1]:2===r?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}}r("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError),r("ERR_INVALID_ARG_TYPE",function(e,t,r){var o,s,a,c;let u,l;if("string"==typeof t&&(o="not ",t.substr(0,o.length)===o)?(u="must not be",t=t.replace(/^not /,"")):u="must be",s=" argument",(void 0===a||a>e.length)&&(a=e.length),e.substring(a-s.length,a)===s)l=`The ${e} ${u} ${i(t,"type")}`;else{let r=("number"!=typeof c&&(c=0),c+1>e.length||-1===e.indexOf(".",c))?"argument":"property";l=`The "${e}" ${r} ${u} ${i(t,"type")}`}return l+`. Received type ${typeof r}`},TypeError),r("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),r("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"}),r("ERR_STREAM_PREMATURE_CLOSE","Premature close"),r("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"}),r("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),r("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),r("ERR_STREAM_WRITE_AFTER_END","write after end"),r("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),r("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError),r("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),e.exports.F=t},116:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(2652),a=r(9306),c=r(8551),u=r(1767),l=r(9539),f=r(4549)("find",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:f},{find:function(e){c(this);try{a(e)}catch(e){l(this,"throw",e)}if(f)return o(f,this,e);var t=u(this),r=0;return s(t,function(t,i){if(e(t,r++))return i(t)},{IS_RECORD:!0,INTERRUPTED:!0}).result}})},157:(e,t,r)=>{"use strict";e.exports={DEFAULT_SOURCE:["kugou","kuwo","migu","ytdlp"],PROVIDERS:{qq:r(9717),kugou:r(1698),kuwo:r(9873),migu:r(629),joox:r(7407),youtube:r(1358),youtubedl:r(5903),ytdlp:r(5971),bilibili:r(5835),bilivideo:r(9986),pyncmd:r(3922)}}},181:e=>{"use strict";e.exports=require("buffer")},230:(e,t,r)=>{"use strict";var i=r(6518),o=r(9306),s=r(6194),a=r(6223),c=TypeError;i({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=s(this),r=arguments.length<2,i=r?void 0:arguments[1];if(o(e),a(t,function(o,s){r?(r=!1,i=o):i=e(i,o,s,t)}),r)throw new c("Reduce of empty map with no initial value");return i}})},283:(e,t,r)=>{"use strict";var i=r(9504),o=r(9039),s=r(4901),a=r(9297),c=r(3724),u=r(350).CONFIGURABLE,l=r(3706),f=r(1181),p=f.enforce,h=f.get,d=String,y=Object.defineProperty,g=i("".slice),m=i("".replace),b=i([].join),v=c&&!o(function(){return 8!==y(function(){},"length",{value:8}).length}),w=String(String).split("String"),_=e.exports=function(e,t,r){"Symbol("===g(d(t),0,7)&&(t="["+m(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||u&&e.name!==t)&&(c?y(e,"name",{value:t,configurable:!0}):e.name=t),v&&r&&a(r,"arity")&&e.length!==r.arity&&y(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?c&&y(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var i=p(e);return a(i,"source")||(i.source=b(w,"string"==typeof t?t:"")),e};Function.prototype.toString=_(function(){return s(this)&&h(this).source||l(this)},"toString")},321:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(3440);i({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){return o(a,this,s(e))}})},324:(e,t,r)=>{"use strict";let i=r(9844)({circles:!0}),o=r(4075),s=r(5146),a=r(8463),{isMainThread:c}=r(8167),u=r(7589)(),{DATE_FORMAT:l,ERROR_LIKE_KEYS:f,MESSAGE_KEY:p,LEVEL_KEY:h,LEVEL_LABEL:d,TIMESTAMP_KEY:y,LOGGER_KEYS:g,LEVELS:m}=r(4160);function b(e,t=!1){if(!1===t)return e;let r=v(e);if(!w(r))return e;if(!0===t)return o(r,"UTC:"+l);let i=t.toUpperCase();if("SYS:STANDARD"===i)return o(r,l);let s=i.substr(0,4);return"SYS:"===s||"UTC:"===s?"UTC:"===s?o(r,t):o(r,t.slice(4)):o(r,`UTC:${t}`)}function v(e){let t=new Date(e);return w(t)?t:t=new Date(+e)}function w(e){return e instanceof Date&&!Number.isNaN(e.getTime())}function _(e){return"[object Object]"===Object.prototype.toString.apply(e)}function S({input:e,ident:t="    ",eol:r=`
`}){let i=e.split(/\r?\n/);for(let e=1;e<i.length;e+=1)i[e]=t+i[e];return i.join(r)}function x({input:e,ident:t="    ",eol:r=`
`,skipKeys:i=[],customPrettifiers:o={},errorLikeKeys:s=f,excludeLoggerKeys:c=!0,singleLine:l=!1,colorizer:p=u}){let h=[].concat(i);!0===c&&Array.prototype.push.apply(h,g);let d="",{plain:y,errors:m}=Object.entries(e).reduce(({plain:t,errors:r},[i,a])=>{if(!1===h.includes(i)){let c="function"==typeof o[i]?o[i](a,i,e):a;s.includes(i)?r[i]=c:t[i]=c}return{plain:t,errors:r}},{plain:{},errors:{}});return l?(Object.keys(y).length>0&&(d+=p.greyMessage(a(y))),d+=r):Object.entries(y).forEach(([e,i])=>{let s="function"==typeof o[e]?i:a(i,null,2);if(void 0===s)return;let c=S({input:s,ident:t,eol:r});d+=`${t}${e}:${c.startsWith(r)?"":" "}${c}${r}`}),Object.entries(m).forEach(([e,i])=>{let s="function"==typeof o[e]?i:a(i,null,2);void 0!==s&&(d+=E({keyName:e,lines:s,eol:r,ident:t}))}),d}function E({keyName:e,lines:t,eol:r,ident:i}){let o="",s=S({input:t,ident:i,eol:r}),a=`${i}${e}: ${s}${r}`.split(r);for(let e=0;e<a.length;e+=1){0!==e&&(o+=r);let t=a[e];if(/^\s*"stack"/.test(t)){let e=/^(\s*"stack":)\s*(".*"),?$/.exec(t);if(e&&3===e.length){let i=/^\s*/.exec(t)[0].length+4,s=" ".repeat(i),a=e[2];o+=e[1]+r+s+JSON.parse(a).replace(/\n/g,r+s)}else o+=t}else o+=t}return o}function O(e){let t=[],r=!1,i="";for(let o=0;o<e.length;o++){let s=e.charAt(o);if("\\"===s){r=!0;continue}if(r){r=!1,i+=s;continue}if("."===s){t.push(i),i="";continue}i+=s}return i.length&&t.push(i),t}function k(e,t){let r=O(t),i=r.pop();r.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&(e=e[t])}),delete e[i]}function j(){}function T(e,t){e.destroyed||("beforeExit"===t?(e.flush(),e.on("drain",function(){e.end()})):e.flushSync())}e.exports={isObject:_,prettifyErrorLog:function({log:e,messageKey:t=p,ident:r="    ",eol:i=`
`,errorLikeKeys:o=f,errorProperties:s=[]}){let a=S({input:e.stack,ident:r,eol:i}),c=`${r}${a}${i}`;if(s.length>0){let a,u=g.concat(t,"type","stack");a="*"===s[0]?Object.keys(e).filter(e=>!1===u.includes(e)):s.filter(e=>!1===u.includes(e));for(let t=0;t<a.length;t+=1){let s=a[t];if(s in e!=!1){if(_(e[s])){let t=x({input:e[s],errorLikeKeys:o,excludeLoggerKeys:!1,eol:i,ident:r+r});c=`${c}${r}${s}: {${i}${t}${r}}${i}`;continue}c=`${c}${r}${s}: ${e[s]}${i}`}}}return c},prettifyLevel:function({log:e,colorizer:t=u,levelKey:r=h,prettifier:i,customLevels:o,customLevelNames:s}){if(r in e==!1)return;let a=e[r];return i?i(a):t(a,{customLevels:o,customLevelNames:s})},prettifyMessage:function({log:e,messageFormat:t,messageKey:r=p,colorizer:i=u,levelLabel:o=d,levelKey:s=h,customLevels:a,useOnlyCustomProps:c}){if(t&&"string"==typeof t){let r=String(t).replace(/{([^{}]+)}/g,function(t,r){return r===o&&e[s]?(c?void 0===a:void 0===a[e[s]])?m[e[s]]:a[e[s]]:r.split(".").reduce(function(e,t){return e&&e[t]?e[t]:""},e)});return i.message(r)}if(t&&"function"==typeof t){let s=t(e,r,o);return i.message(s)}if(r in e!=!1&&"string"==typeof e[r])return i.message(e[r])},prettifyMetadata:function({log:e,prettifiers:t={}}){let r="";if(e.name||e.pid||e.hostname){if(r+="(",e.name&&(r+=t.name?t.name(e.name):e.name),e.pid){let i=t.pid?t.pid(e.pid):e.pid;e.name&&e.pid?r+="/"+i:r+=i}e.hostname&&(r+=`${"("===r?"on":" on"} ${t.hostname?t.hostname(e.hostname):e.hostname}`),r+=")"}if(e.caller&&(r+=`${""===r?"":" "}<${t.caller?t.caller(e.caller):e.caller}>`),""!==r)return r},prettifyObject:x,prettifyTime:function({log:e,timestampKey:t=y,translateFormat:r,prettifier:i}){let o=null;if(t in e?o=e[t]:"timestamp"in e&&(o=e.timestamp),null===o)return;let s=r?b(o,r):o;return i?i(s):`[${s}]`},buildSafeSonicBoom:function(e){let t=new s(e);return t.on("error",function e(r){if("EPIPE"===r.code){t.write=j,t.end=j,t.flushSync=j,t.destroy=j;return}t.removeListener("error",e)}),!e.sync&&c&&function(e){if(global.WeakRef&&global.WeakMap&&global.FinalizationRegistry){let t=r(6270);t.register(e,T),e.on("close",function(){t.unregister(e)})}}(t),t},filterLog:function(e,t){let r=i(e);return t.forEach(e=>{k(r,e)}),r}},e.exports.internals={formatTime:b,joinLinesWithIndentation:S,prettifyError:E,deleteLogProperty:k,splitIgnoreKey:O,createDate:v,isValidDate:w}},345:(e,t,r)=>{"use strict";function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var i,o,a;i=e,o=t,a=r[t],(o=s(o))in i?Object.defineProperty(i,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var a=r(181).Buffer,c=r(9023).inspect,u=c&&c.custom||"inspect";e.exports=function(){var e;function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.head=null,this.tail=null,this.length=0}return e=[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r}},{key:"concat",value:function(e){if(0===this.length)return a.alloc(0);for(var t,r,i=a.allocUnsafe(e>>>0),o=this.head,s=0;o;)t=o.data,r=s,a.prototype.copy.call(t,i,r),s+=o.data.length,o=o.next;return i}},{key:"consume",value:function(e,t){var r;return e<this.head.data.length?(r=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):r=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,r=1,i=t.data;for(e-=i.length;t=t.next;){var o=t.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),0==(e-=s)){s===o.length?(++r,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=o.slice(s));break}++r}return this.length-=r,i}},{key:"_getBuffer",value:function(e){var t=a.allocUnsafe(e),r=this.head,i=1;for(r.data.copy(t),e-=r.data.length;r=r.next;){var o=r.data,s=e>o.length?o.length:e;if(o.copy(t,t.length-e,0,s),0==(e-=s)){s===o.length?(++i,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=o.slice(s));break}++i}return this.length-=i,t}},{key:u,value:function(e,t){return c(this,o(o({},t),{},{depth:0,customInspect:!1}))}}],function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,s(i.key),i)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}()},350:(e,t,r)=>{"use strict";var i=r(3724),o=r(9297),s=Function.prototype,a=i&&Object.getOwnPropertyDescriptor,c=o(s,"name"),u=c&&(!i||i&&a(s,"name").configurable);e.exports={EXISTS:c,PROPER:c&&"something"===(function(){}).name,CONFIGURABLE:u}},397:(e,t,r)=>{"use strict";e.exports=r(7751)("document","documentElement")},421:e=>{"use strict";e.exports={}},478:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1517),r(1379),r(3777),r(4190),r(2359),r(6097),r(7273),r(7415),r(9929),r(7583),r(5122),r(230),r(7268),r(9733),r(7333),r(4520),r(8992),r(6167),r(1393),r(1454),r(4905),r(8872),r(3518),r(67),r(9920),r(3949);let i=r(6900),o=r(9664),{PROVIDERS:s,DEFAULT_SOURCE:a}=r(157),{isHostWrapper:c}=r(5853),u=r(2651),l=r(1196),f=r(4470),{logScope:p}=r(5469),h=r(9418),d=p("provider/match"),y=new Map([["bilivideo.com","https://www.bilibili.com/"],["upos-hz-mirrorakam.akamaized.net","https://www.bilibili.com/"]]);async function g(e,t){d.debug({source:e,info:t},"Getting the audio...");let r=await s[e].check(t);if(!r)throw new u(e);let i=await m(r);if(d.debug(i,"The matched song is:"),!i||"string"!=typeof i.url)throw new f("song is undefined, or song.url is not a string.");return d.debug({source:e,info:t},"The audio matched!"),{...i,source:e}}async function m(e){let t,r=c(e),i={size:0,br:null,url:null,md5:null},s={range:"bytes=0-8191","accept-encoding":"identity"};y.forEach((e,t)=>{r(t)&&(s.referer=e)});let a=await o("GET",e,s),{headers:u}=a;if(!((t=a.statusCode)>=200&&t<=299))throw new l(e,a.statusCode);i.url=a.url.href;let f=await a.body(!0);try{let e=function(e){let t={3:{3:["free",32,64,96,128,160,192,224,256,288,320,352,384,416,448,"bad"],2:["free",32,48,56,64,80,96,112,128,160,192,224,256,320,384,"bad"],1:["free",32,40,48,56,64,80,96,112,128,160,192,224,256,320,"bad"]},2:{3:["free",32,48,56,64,80,96,112,128,144,160,176,192,224,256,"bad"],2:["free",8,16,24,32,40,48,56,64,80,96,112,128,144,160,"bad"]}};t[2][1]=t[2][2],t[0]=t[2];let r=0;if("fLaC"===e.slice(0,4).toString())return 999;"ID3"===e.slice(0,3).toString()&&(r=6,r=10+e.slice(r,r+4).reduce((e,t,r)=>e+(127&t)<<7*(3-r),0));let i=e.slice(r,r+4);if(4===i.length&&255===i[0]&&(i[1]>>5&7)==7&&(i[1]>>1&3)!=0&&(i[2]>>4&15)!=15&&(i[2]>>2&3)!=3){let e=i[1]>>3&3,r=i[1]>>1&3,o=i[2]>>4;return t[e][r][o]}}(f);i.br=e&&!isNaN(e)?1e3*e:null}catch(e){d.debug(e,"Failed to decode and extract the bitrate")}if(!i.br){if(r("qq.com")&&i.url.includes(".m4a")&&(i.br=96e3),r("bilivideo.com")&&i.url.includes(".m4a")){let e=parseInt(i.url.match(/-(\d+)k\.m4a/));isNaN(e)||e<96||e>999?e=192e3:e*=1e3,i.br=e}r("googlevideo.com")&&(i.br=128e3)}return u&&(r("126.net")&&(i.md5=i.url.split("/").slice(-1)[0].replace(/\..*/g,"")),r("qq.com")&&(i.md5=u["server-md5"]),i.size=parseInt((u["content-range"]||"").split("/").pop()||u["content-length"])||0,"8192"!==u["content-length"])?Promise.reject():i}e.exports=async function e(e,t,r){let o=(t||global.source||a).filter(e=>e in s),c=await i(e,r),l=null;if(process.env.SELECT_MAX_BR){let e=await Promise.allSettled(o.map(async e=>g(e,c).catch(e=>{throw e&&(e instanceof h?d.debug(e):d.error(e)),e})));if(0===(e=e.filter(e=>"fulfilled"===e.status)).length)throw new u("any source");l=(e=e.map(e=>e.value)).reduce((e,t)=>e.br>=t.br?e:t)}else if(process.env.FOLLOW_SOURCE_ORDER){for(let e=0;e<o.length;e++){let t=o[e];try{l=await g(t,c);break}catch(e){e&&(e instanceof h?d.debug(e):d.error(e))}}if(!l)throw"No audioData!"}else l=await Promise.any(o.map(async e=>g(e,c).catch(e=>{throw e&&(e instanceof h?d.debug(e):d.error(e)),e})));let{id:f,name:p}=c,{url:y}=l;return d.debug({audioInfo:c,audioData:l},"The data to replace:"),d.info({audioId:f,songName:p,url:y},`Replaced: [${f}] ${p}`),l}},507:(e,t,r)=>{"use strict";var i=r(9565);e.exports=function(e,t,r){for(var o,s,a=r?e:e.iterator,c=e.next;!(o=i(c,a)).done;)if(void 0!==(s=t(o.value)))return s}},537:(e,t,r)=>{"use strict";var i=r(550),o=r(4428);e.exports=r(916).CONSTRUCTOR||!o(function(e){i.all(e).then(void 0,function(){})})},544:e=>{"use strict";let t=Symbol("pino.setLevel"),r=Symbol("pino.getLevel"),i=Symbol("pino.levelVal"),o=Symbol("pino.useLevelLabels"),s=Symbol("pino.useOnlyCustomLevels"),a=Symbol("pino.mixin"),c=Symbol("pino.lsCache"),u=Symbol("pino.chindings"),l=Symbol("pino.parsedChindings"),f=Symbol("pino.asJson"),p=Symbol("pino.write"),h=Symbol("pino.redactFmt"),d=Symbol("pino.time"),y=Symbol("pino.timeSliceIndex"),g=Symbol("pino.stream"),m=Symbol("pino.stringify"),b=Symbol("pino.stringifiers"),v=Symbol("pino.end"),w=Symbol("pino.formatOpts"),_=Symbol("pino.messageKey"),S=Symbol("pino.nestedKey"),x=Symbol("pino.mixinMergeStrategy"),E=Symbol("pino.wildcardFirst"),O=Symbol.for("pino.serializers"),k=Symbol.for("pino.formatters"),j=Symbol.for("pino.hooks");e.exports={setLevelSym:t,getLevelSym:r,levelValSym:i,useLevelLabelsSym:o,mixinSym:a,lsCacheSym:c,chindingsSym:u,parsedChindingsSym:l,asJsonSym:f,writeSym:p,serializersSym:O,redactFmtSym:h,timeSym:d,timeSliceIndexSym:y,streamSym:g,stringifySym:m,stringifiersSym:b,endSym:v,formatOptsSym:w,messageKeySym:_,nestedKeySym:S,wildcardFirstSym:E,needsMetadataGsym:Symbol.for("pino.metadata"),useOnlyCustomLevelsSym:s,formattersSym:k,hooksSym:j,mixinMergeStrategySym:x}},550:(e,t,r)=>{"use strict";e.exports=r(4576).Promise},616:(e,t,r)=>{"use strict";e.exports=!r(9039)(function(){var e=(function(){}).bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(3064),r(2577),r(8992);let i=r(2569),o=r(9643),s=r(9664),{getManagedCacheStorage:a}=r(3971),c={origin:"http://music.migu.cn/",referer:"http://m.music.migu.cn/v3/",aversionid:process.env.MIGU_COOKIE||null,channel:"0146921"},u=e=>{let t=e.singerId.split(/\s*,\s*/),r=e.singerName.split(/\s*,\s*/);return{id:e.id,name:e.title,album:{id:e.albumId,name:e.albumName},artists:t.map((e,t)=>({id:e,name:r[t]}))}},l=e=>Promise.all(["ZQ24","SQ","HQ","PQ"].slice(2*!o.ENABLE_FLAC).map(t=>((e,t)=>{let r="https://app.c.nf.migu.cn/MIGUM2.0/strategy/listen-url/v2.4?netType=01&resourceType=2&songId="+e.toString()+"&toneFlag="+t;return s("GET",r,c).then(e=>e.json()).then(e=>{let{audioFormatType:i}=e.data;return i!==t?Promise.reject():r?e.data.url:Promise.reject()})})(e,t).catch(()=>null))).then(e=>e.find(e=>e)||Promise.reject()).catch(()=>i().migu.track(e)),f=a("provider/migu");e.exports={check:e=>f.cache(e,()=>(e=>s("GET","https://m.music.migu.cn/migu/remoting/scr_search_tag?keyword="+encodeURIComponent(e.keyword)+"&type=2&rows=20&pgc=1",c).then(e=>e.json()).then(t=>{let r=o(((t||{}).musics||[]).map(u),e);return r?r.id:Promise.reject()}))(e)).then(l),track:l}},655:(e,t,r)=>{"use strict";var i=r(6955),o=String;e.exports=function(e){if("Symbol"===i(e))throw TypeError("Cannot convert a Symbol value to a string");return o(e)}},679:(e,t,r)=>{"use strict";var i=r(1625),o=TypeError;e.exports=function(e,t){if(i(t,e))return e;throw new o("Incorrect invocation")}},684:e=>{"use strict";e.exports=function(e,t){var r="function"==typeof Iterator&&Iterator.prototype[e];if(r)try{r.call({next:null},t).next()}catch(e){return!0}}},741:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var i=+e;return(i>0?r:t)(i)}},747:(e,t,r)=>{"use strict";var i=r(6699),o=r(6193),s=r(6249),a=Error.captureStackTrace;e.exports=function(e,t,r,c){s&&(a?a(e,t):i(e,"stack",o(r,c)))}},754:(e,t,r)=>{"use strict";let i=r(61),{lsCacheSym:o,levelValSym:s,useOnlyCustomLevelsSym:a,streamSym:c,formattersSym:u,hooksSym:l}=r(544),{noop:f,genLog:p}=r(6526),h={trace:10,debug:20,info:30,warn:40,error:50,fatal:60},d={fatal:e=>{let t=p(h.fatal,e);return function(...e){let r=this[c];if(t.call(this,...e),"function"==typeof r.flushSync)try{r.flushSync()}catch(e){}}},error:e=>p(h.error,e),warn:e=>p(h.warn,e),info:e=>p(h.info,e),debug:e=>p(h.debug,e),trace:e=>p(h.trace,e)},y=Object.keys(h).reduce((e,t)=>(e[h[t]]=t,e),{});e.exports={initialLsCache:Object.keys(y).reduce((e,t)=>(e[t]=i('{"level":'+Number(t)),e),{}),genLsCache:function(e){let t=e[u].level,{labels:r}=e.levels,i={};for(let e in r){let o=t(r[e],Number(e));i[e]=JSON.stringify(o).slice(0,-1)}return e[o]=i,e},levelMethods:d,getLevel:function(e){let{levels:t,levelVal:r}=this;return t&&t.labels?t.labels[r]:""},setLevel:function(e){let{labels:t,values:r}=this.levels;if("number"==typeof e){if(void 0===t[e])throw Error("unknown level value"+e);e=t[e]}if(void 0===r[e])throw Error("unknown level "+e);let i=this[s],o=this[s]=r[e],c=this[a],u=this[l].logMethod;for(let e in r){if(o>r[e]){this[e]=f;continue}this[e]=!function(e,t){if(t)return!1;switch(e){case"fatal":case"error":case"warn":case"info":case"debug":case"trace":return!0;default:return!1}}(e,c)?p(r[e],u):d[e](u)}this.emit("level-change",e,o,t[i],i)},isLevelEnabled:function(e){let{values:t}=this.levels,r=t[e];return void 0!==r&&r>=this[s]},mappings:function(e=null,t=!1){let r=e?Object.keys(e).reduce((t,r)=>(t[e[r]]=r,t),{}):null;return{labels:Object.assign(Object.create(Object.prototype,{Infinity:{value:"silent"}}),t?null:y,r),values:Object.assign(Object.create(Object.prototype,{silent:{value:1/0}}),t?null:h,e)}},assertNoLevelCollisions:function(e,t){let{labels:r,values:i}=e;for(let e in t){if(e in i)throw Error("levels cannot be overridden");if(t[e]in r)throw Error("pre-existing level values cannot be used for new levels")}},assertDefaultLevelFound:function(e,t,r){if("number"==typeof e){if(![].concat(Object.keys(t||{}).map(e=>t[e]),r?[]:Object.keys(y).map(e=>+e),1/0).includes(e))throw Error(`default level:${e} must be included in custom levels`);return}if(!(e in Object.assign(Object.create(Object.prototype,{silent:{value:1/0}}),r?null:h,t)))throw Error(`default level:${e} must be included in custom levels`)}}},757:(e,t,r)=>{"use strict";var i=r(7751),o=r(4901),s=r(1625),a=r(7040),c=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=i("Symbol");return o(t)&&s(t.prototype,c(e))}},772:(e,t,r)=>{"use strict";var i=r(9565),o=r(7751),s=r(5966);e.exports=function(e,t,r,a){try{var c=s(e,"return");if(c)return o("Promise").resolve(i(c,e)).then(function(){t(r)},function(e){a(e)})}catch(e){return a(e)}t(r)}},793:e=>{"use strict";function t(e){try{return JSON.stringify(e)}catch(e){return'"[Circular]"'}}e.exports=function(e,r,i){var o=i&&i.stringify||t;if("object"==typeof e&&null!==e){var s=r.length+1;if(1===s)return e;var a=Array(s);a[0]=o(e);for(var c=1;c<s;c++)a[c]=o(r[c]);return a.join(" ")}if("string"!=typeof e)return e;var u=r.length;if(0===u)return e;for(var l="",f=0,p=-1,h=e&&e.length||0,d=0;d<h;){if(37===e.charCodeAt(d)&&d+1<h){switch(p=p>-1?p:0,e.charCodeAt(d+1)){case 100:case 102:if(f>=u||null==r[f])break;p<d&&(l+=e.slice(p,d)),l+=Number(r[f]),p=d+2,d++;break;case 105:if(f>=u||null==r[f])break;p<d&&(l+=e.slice(p,d)),l+=Math.floor(Number(r[f])),p=d+2,d++;break;case 79:case 111:case 106:if(f>=u||void 0===r[f])break;p<d&&(l+=e.slice(p,d));var y=typeof r[f];if("string"===y){l+="'"+r[f]+"'",p=d+2,d++;break}if("function"===y){l+=r[f].name||"<anonymous>",p=d+2,d++;break}l+=o(r[f]),p=d+2,d++;break;case 115:if(f>=u)break;p<d&&(l+=e.slice(p,d)),l+=String(r[f]),p=d+2,d++;break;case 37:p<d&&(l+=e.slice(p,d)),l+="%",p=d+2,d++,f--}++f}++d}return -1===p?e:(p<h&&(l+=e.slice(p)),l)}},809:(e,t,r)=>{"use strict";let i=r(9896),o=r(4434),s=r(61),a=r(9023).inherits,c=r(7814);function u(e,t){function r(r,i){if(r){t._reopening=!1,t._writing=!1,t._opening=!1,t.sync?process.nextTick(()=>{t.listenerCount("error")>0&&t.emit("error",r)}):t.emit("error",r);return}if(t.fd=i,t.file=e,t._reopening=!1,t._opening=!1,t._writing=!1,t.sync?process.nextTick(()=>t.emit("ready")):t.emit("ready"),t._reopening)return;let o=t._buf.length;o>0&&o>t.minLength&&!t.destroyed&&p(t)}if(t._opening=!0,t._writing=!0,t._asyncDrainScheduled=!1,t.sync)try{let t=i.openSync(e,"a");r(null,t)}catch(e){throw r(e),e}else i.open(e,"a",r)}function l(e){if(!(this instanceof l))return new l(e);let{fd:t,dest:r,minLength:o,sync:s}=e||{};if(t=t||r,this._buf="",this.fd=-1,this._writing=!1,this._writingBuf="",this._ending=!1,this._reopening=!1,this._asyncDrainScheduled=!1,this.file=null,this.destroyed=!1,this.sync=s||!1,this.minLength=o||0,"number"==typeof t)this.fd=t,process.nextTick(()=>this.emit("ready"));else if("string"==typeof t)u(t,this);else throw Error("SonicBoom supports only file descriptors and files");this.release=(e,t)=>{if(e){if("EAGAIN"===e.code)if(this.sync)try{c(100),this.release(void 0,0)}catch(e){this.release(e)}else setTimeout(()=>{i.write(this.fd,this._writingBuf,"utf8",this.release)},100);else this._buf=this._writingBuf+this._buf,this._writingBuf="",this._writing=!1,this.emit("error",e);return}if(this._writingBuf.length!==t){if(this._writingBuf=this._writingBuf.slice(t),!this.sync)return void i.write(this.fd,this._writingBuf,"utf8",this.release);try{do t=i.writeSync(this.fd,this._writingBuf,"utf8"),this._writingBuf=this._writingBuf.slice(t);while(0!==this._writingBuf.length)}catch(e){this.release(e);return}}if(this._writingBuf="",this.destroyed)return;let r=this._buf.length;this._reopening?(this._writing=!1,this._reopening=!1,this.reopen()):r>0&&r>this.minLength?p(this):this._ending?r>0?p(this):(this._writing=!1,h(this)):(this._writing=!1,this.sync?this._asyncDrainScheduled||(this._asyncDrainScheduled=!0,process.nextTick(f,this)):this.emit("drain"))},this.on("newListener",function(e){"drain"===e&&(this._asyncDrainScheduled=!1)})}function f(e){e.listenerCount("drain")>0&&(e._asyncDrainScheduled=!1,e.emit("drain"))}function p(e){e._writing=!0;let t=e._buf,r=e.release;if(t.length>0x1000000?(t=t.slice(0,0x1000000),e._buf=e._buf.slice(0x1000000)):e._buf="",s(t),e._writingBuf=t,e.sync)try{let o=i.writeSync(e.fd,t,"utf8");r(null,o)}catch(e){r(e)}else i.write(e.fd,t,"utf8",r)}function h(e){if(-1===e.fd)return void e.once("ready",h.bind(null,e));i.close(e.fd,t=>{if(t)return void e.emit("error",t);e._ending&&!e._writing&&e.emit("finish"),e.emit("close")}),e.destroyed=!0,e._buf=""}a(l,o),l.prototype.write=function(e){if(this.destroyed)throw Error("SonicBoom destroyed");this._buf+=e;let t=this._buf.length;return!this._writing&&t>this.minLength&&p(this),t<16384},l.prototype.flush=function(){if(this.destroyed)throw Error("SonicBoom destroyed");this._writing||this.minLength<=0||p(this)},l.prototype.reopen=function(e){if(this.destroyed)throw Error("SonicBoom destroyed");if(this._opening)return void this.once("ready",()=>{this.reopen(e)});if(this._ending)return;if(!this.file)throw Error("Unable to reopen a file descriptor, you must pass a file to SonicBoom");if(this._reopening=!0,this._writing)return;let t=this.fd;this.once("ready",()=>{t!==this.fd&&i.close(t,e=>{if(e)return this.emit("error",e)})}),u(e||this.file,this)},l.prototype.end=function(){if(this.destroyed)throw Error("SonicBoom destroyed");return this._opening?void this.once("ready",()=>{this.end()}):this._ending?void 0:(this._ending=!0,!this._writing&&this._buf.length>0&&this.fd>=0)?void p(this):void(!this._writing&&h(this))},l.prototype.flushSync=function(){if(this.destroyed)throw Error("SonicBoom destroyed");if(this.fd<0)throw Error("sonic boom is not ready yet");for(;this._buf.length>0;)try{i.writeSync(this.fd,this._buf,"utf8"),this._buf=""}catch(e){if("EAGAIN"!==e.code)throw e;c(100)}},l.prototype.destroy=function(){this.destroyed||h(this)},e.exports=l},815:(e,t,r)=>{var i,o=r(3519),s=r(6611);try{i=r(9896)}catch(e){}var a=function(){},c="undefined"!=typeof process&&/^v?\.0/.test(process.version),u=function(e){return"function"==typeof e},l=function(e,t,r,l){l=o(l);var f=!1;e.on("close",function(){f=!0}),s(e,{readable:t,writable:r},function(e){if(e)return l(e);f=!0,l()});var p=!1;return function(t){if(!f&&!p){if(p=!0,c&&i&&(e instanceof(i.ReadStream||a)||e instanceof(i.WriteStream||a))&&u(e.close))return e.close(a);if(e.setHeader&&u(e.abort))return e.abort();if(u(e.destroy))return e.destroy();l(t||Error("stream was destroyed"))}}},f=function(e){e()},p=function(e,t){return e.pipe(t)};e.exports=function(){var e,t=Array.prototype.slice.call(arguments),r=u(t[t.length-1]||a)&&t.pop()||a;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw Error("pump requires two streams per minimum");var i=t.map(function(o,s){var a=s<t.length-1;return l(o,a,s>0,function(t){e||(e=t),t&&i.forEach(f),a||(i.forEach(f),r(e))})});return t.reduce(p)}},851:(e,t,r)=>{"use strict";var i=r(6955),o=r(5966),s=r(4117),a=r(6269),c=r(8227)("iterator");e.exports=function(e){if(!s(e))return o(e,c)||o(e,"@@iterator")||a[i(e)]}},857:e=>{"use strict";e.exports=require("os")},916:(e,t,r)=>{"use strict";var i=r(4576),o=r(550),s=r(4901),a=r(2796),c=r(3706),u=r(8227),l=r(4215),f=r(6395),p=r(9519),h=o&&o.prototype,d=u("species"),y=!1,g=s(i.PromiseRejectionEvent);e.exports={CONSTRUCTOR:a("Promise",function(){var e=c(o),t=e!==String(o);if(!t&&66===p||f&&!(h.catch&&h.finally))return!0;if(!p||p<51||!/native code/.test(e)){var r=new o(function(e){e(1)}),i=function(e){e(function(){},function(){})};if((r.constructor={})[d]=i,!(y=r.then(function(){})instanceof i))return!0}return!t&&("BROWSER"===l||"DENO"===l)&&!g}),REJECTION_EVENT:g,SUBCLASSING:y}},1056:(e,t,r)=>{"use strict";var i=r(4913).f;e.exports=function(e,t,r){r in e||i(e,r,{configurable:!0,get:function(){return t[r]},set:function(e){t[r]=e}})}},1072:(e,t,r)=>{"use strict";var i=r(1828),o=r(8727);e.exports=Object.keys||function(e){return i(e,o)}},1103:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},1181:(e,t,r)=>{"use strict";var i,o,s,a=r(8622),c=r(4576),u=r(34),l=r(6699),f=r(9297),p=r(7629),h=r(6119),d=r(421),y="Object already initialized",g=c.TypeError,m=c.WeakMap;if(a||p.state){var b=p.state||(p.state=new m);b.get=b.get,b.has=b.has,b.set=b.set,i=function(e,t){if(b.has(e))throw new g(y);return t.facade=e,b.set(e,t),t},o=function(e){return b.get(e)||{}},s=function(e){return b.has(e)}}else{var v=h("state");d[v]=!0,i=function(e,t){if(f(e,v))throw new g(y);return t.facade=e,l(e,v,t),t},o=function(e){return f(e,v)?e[v]:{}},s=function(e){return f(e,v)}}e.exports={set:i,get:o,has:s,enforce:function(e){return s(e)?o(e):i(e,{})},getterFor:function(e){return function(t){var r;if(!u(t)||(r=o(t)).type!==e)throw new g("Incompatible receiver, "+e+" required");return r}}}},1196:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e,t){super(`Failed to get the response. Status code: ${t}`),this.url=e,this.code=t,this.name="RequestFailed"}}},1291:(e,t,r)=>{"use strict";var i=r(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:i(t)}},1358:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4905),r(8872),r(8992),r(1393),r(1454),r(8335),r(3064),r(2577);let i=r(9664),{getManagedCacheStorage:o}=r(3971),s=o("provider/youtube"),a=void 0,c=process.env.YOUTUBE_KEY||null,u=e=>i("GET",`https://www.youtube.com/watch?v=${e}`,{},null,a).then(e=>e.body()).then(e=>JSON.parse(e.match(/ytInitialPlayerResponse\s*=\s*{[^]+};\s*var\s*meta/)[0].replace(/;var meta/,"").replace(/ytInitialPlayerResponse = /,"")).streamingData).then(e=>{let t=e.formats.concat(e.adaptiveFormats).find(e=>140===e.itag),r=(t.signatureCipher||"").split("&").reduce((e,t)=>{let r=t.split("=").map(decodeURIComponent);return Object.assign({},e,{[r[0]]:r[1]})},{});return t.url||(r.sp.includes("sig")?s.cache("YOUTUBE_SIGNATURE",()=>((e="-tKVN2mAKRI")=>i("GET",`https://www.youtube.com/watch?v=${e}`,{},null,a).then(e=>e.body()).then(e=>{let t=/"WEB_PLAYER_CONTEXT_CONFIG_ID_KEVLAR_VERTICAL_LANDING_PAGE_PROMO":{[^}]+}/.exec(e)[0];return i("GET","https://youtube.com"+(t=JSON.parse(`{${t}}}`).WEB_PLAYER_CONTEXT_CONFIG_ID_KEVLAR_VERTICAL_LANDING_PAGE_PROMO).jsUrl,{},null,a).then(e=>e.body())}).then(e=>{let[,t,r]=/function\((\w+)\)\s*{([^}]+split\(""\)[^}]+join\(""\))};/.exec(e),i=/;(.+?)\..+?\(/.exec(r)[1];return Function([t],RegExp(`var ${i}={[\\s\\S]+?};`).exec(e)[0]+`
`+r)}))(),Date.now()+864e5).then(e=>r.url+"&sig="+e(r.s)):r.url)});e.exports={check:e=>s.cache(e,()=>c?i("GET",`https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(e.keyword)}&type=video&key=${c}`,{accept:"application/json"},null,a).then(e=>e.json()).then(e=>{let t=e.items[0];return t?t.id.videoId:Promise.reject()}):i("GET",`https://www.youtube.com/results?search_query=${encodeURIComponent(e.keyword)}`,{},null,a).then(e=>e.body()).then(e=>{let t=JSON.parse(e.match(/ytInitialData\s*=\s*([^;]+);/)[1]).contents.twoColumnSearchResultsRenderer.primaryContents.sectionListRenderer.contents[0].itemSectionRenderer.contents[1];return t?t.videoRenderer.videoId:Promise.reject()})).then(u),track:u}},1379:(e,t,r)=>{"use strict";var i=r(6518),o=r(6194),s=r(2248),a=s.get,c=s.has,u=s.set;i({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var r,i,s=o(this);return c(s,e)?(r=a(s,e),"update"in t&&(r=t.update(r,e,s),u(s,e,r)),r):(i=t.insert(e,s),u(s,e,i),i)}})},1385:(e,t,r)=>{"use strict";var i=r(9539);e.exports=function(e,t,r){for(var o=e.length-1;o>=0;o--)if(void 0!==e[o])try{r=i(e[o].iterator,t,r)}catch(e){t="throw",r=e}if("throw"===t)throw r;return r}},1393:(e,t,r)=>{"use strict";r(6518)({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{map:r(1750)})},1416:(e,t,r)=>{e.exports=r(2203)},1423:(e,t,r)=>{"use strict";let i=r(6306),o=r(3288),s=r(8103),a=r(2187),{groupRedact:c,nestedRedact:u}=r(7157),l=r(6754),f=r(1753),p=i(),h=e=>e;function d(e={}){let t=Array.from(new Set(e.paths||[])),r="serialize"in e&&(!1===e.serialize||"function"==typeof e.serialize)?e.serialize:JSON.stringify,i=e.remove;if(!0===i&&r!==JSON.stringify)throw Error("fast-redact – remove option may only be set when serializer is JSON.stringify");let f=!0===i?void 0:"censor"in e?e.censor:"[REDACTED]",y="function"==typeof f,g=y&&f.length>1;if(0===t.length)return r||h;p({paths:t,serialize:r,censor:f});let{wildcards:m,wcLen:b,secret:v}=o({paths:t,censor:f}),w=a();return s({secret:v,wcLen:b,serialize:r,strict:!("strict"in e)||e.strict,isCensorFct:y,censorFctTakesPath:g},l({secret:v,censor:f,compileRestore:w,serialize:r,groupRedact:c,nestedRedact:u,wildcards:m,wcLen:b}))}h.restore=h,d.rx=f,d.validator=i,e.exports=d},1454:(e,t,r)=>{"use strict";r(1701)},1517:(e,t,r)=>{"use strict";var i=r(6518),o=r(6194),s=r(2248).remove;i({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=o(this),r=!0,i=0,a=arguments.length;i<a;i++)e=s(t,arguments[i]),r=r&&e;return!!r}})},1563:(e,t,r)=>{"use strict";var i=r(6955),o=r(9297),s=r(4117),a=r(8227),c=r(6269),u=a("iterator"),l=Object;e.exports=function(e){if(s(e))return!1;var t=l(e);return void 0!==t[u]||"@@iterator"in t||o(c,i(t))}},1600:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4905),r(8872),r(8992),r(1393),r(1454);let i=r(6982),o=r(7016).parse,s=r(3480).stringify,a="e82ckenh8dichen8",c="rFgB&h#%2?^eDg:Q",u=(e,t)=>{let r=i.createDecipheriv("aes-128-ecb",t,null);return Buffer.concat([r.update(e),r.final()])},l=(e,t)=>{let r=i.createCipheriv("aes-128-ecb",t,null);return Buffer.concat([r.update(e),r.final()])};e.exports={eapi:{encrypt:e=>l(e,a),decrypt:e=>u(e,a),encryptRequest:(t,r)=>{t=o(t);let a=JSON.stringify(r),c=`nobody${t.path}use${a}md5forencrypt`,u=i.createHash("md5").update(c).digest("hex"),l=`${t.path}-36cd479b6b5-${a}-36cd479b6b5-${u}`;return{url:t.href.replace(/\w*api/,"eapi"),body:s({params:e.exports.eapi.encrypt(Buffer.from(l)).toString("hex").toUpperCase()})}}},api:{encryptRequest:(e,t)=>({url:(e=o(e)).href.replace(/\w*api/,"api"),body:s(t)})},linuxapi:{encrypt:e=>l(e,c),decrypt:e=>u(e,c),encryptRequest:(t,r)=>{let i=JSON.stringify({method:"POST",url:(t=o(t)).href,params:r});return{url:t.resolve("/api/linux/forward"),body:s({eparams:e.exports.linuxapi.encrypt(Buffer.from(i)).toString("hex").toUpperCase()})}}},miguapi:{encryptBody:e=>{let t=JSON.stringify(e),r=Buffer.from(i.randomBytes(32).toString("hex")),o=i.randomBytes(8),a=`-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8asrfSaoOb4je+DSmKdriQJKWVJ2oDZrs3wi5W67m3LwTB9QVR+cE3XWU21Nx+YBxS0yun8wDcjgQvYt625ZCcgin2ro/eOkNyUOTBIbuj9CvMnhUYiR61lC1f1IGbrSYYimqBVSjpifVufxtx/I3exReZosTByYp4Xwpb1+WAQIDAQAB
-----END PUBLIC KEY-----`,c=((e,t,r,o)=>{t=t||Buffer.alloc(0);let s=r/8,a=Math.ceil((s+8*o)/32),c=Buffer.concat(Array(a).fill(null).reduce(r=>r.concat(i.createHash("md5").update(Buffer.concat([r.slice(-1)[0],e,t])).digest()),[Buffer.alloc(0)]));return{key:c.slice(0,s),iv:c.slice(s,s+o)}})(r,o,256,16),u=i.createCipheriv("aes-256-cbc",c.key,c.iv);return s({data:Buffer.concat([Buffer.from("Salted__"),o,u.update(Buffer.from(t)),u.final()]).toString("base64"),secKey:i.publicEncrypt({key:a,padding:i.constants.RSA_PKCS1_PADDING},r).toString("base64")})}},base64:{encode:(e,t)=>Buffer.from(e,t).toString("base64").replace(/\+/g,"-").replace(/\//g,"_"),decode:(e,t)=>Buffer.from(e.replace(/-/g,"+").replace(/_/g,"/"),"base64").toString(t)},uri:{retrieve:e=>{e=e.toString().trim();let t="3go8&$8*3*3h0k(2)2",r=Array.from(Array(e.length).keys()).map(r=>String.fromCharCode(e.charCodeAt(r)^t.charCodeAt(r%t.length))).join(""),o=i.createHash("md5").update(r).digest("base64").replace(/\//g,"_").replace(/\+/g,"-");return`http://p1.music.126.net/${o}/${e}`}},md5:{digest:e=>i.createHash("md5").update(e).digest("hex"),pipe:e=>new Promise((t,r)=>{let o=i.createHash("md5").setEncoding("hex");e.pipe(o).on("error",e=>r(e)).once("finish",()=>t(o.read()))})},sha1:{digest:e=>i.createHash("sha1").update(e).digest("hex")},random:{hex:e=>i.randomBytes(Math.ceil(e/2)).toString("hex").slice(0,e)}};try{e.exports.kuwoapi=r(7935)}catch(e){}},1625:(e,t,r)=>{"use strict";e.exports=r(9504)({}.isPrototypeOf)},1632:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(7080),a=r(4402),c=r(8469),u=a.Set,l=a.add;i({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=new u;return c(t,function(e){r(e,e,t)&&l(i,e)}),i}})},1698:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(3064),r(2577),r(8992);let i=r(2569),o=r(9643),s=r(1600),a=r(9664),{getManagedCacheStorage:c}=r(3971),u=e=>({id:e.hash,id_hq:e["320hash"],id_sq:e.sqhash,name:e.songname,duration:1e3*e.duration,album:{id:e.album_id,name:e.album_name}}),l=e=>a("GET","http://mobilecdn.kugou.com/api/v3/search/song?keyword="+encodeURIComponent(e.keyword)+"&page=1&pagesize=10").then(e=>e.json()).then(t=>o(t.data.info.map(u),e)||Promise.reject()).catch(()=>i().kugou.search(e)),f=e=>Promise.all(["sqhash","hqhash","hash"].slice(+!o.ENABLE_FLAC).map(t=>((e,t)=>{let r=()=>{switch(t){case"hash":return e.id;case"hqhash":return e.id_hq;case"sqhash":return e.id_sq}return""};return a("GET","http://trackercdn.kugou.com/i/v2/?key="+s.md5.digest(`${r()}kgcloudv2`)+"&hash="+r()+"&appid=1005&pid=2&cmd=25&behavior=play&album_id="+e.album.id).then(e=>e.json()).then(e=>e.url[0]||Promise.reject())})(e,t).catch(()=>null))).then(e=>e.find(e=>e)||Promise.reject()).catch(()=>i().kugou.track(e)),p=c("provider/kugou");e.exports={check:e=>p.cache(e,()=>l(e)).then(f),search:l}},1701:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(8551),c=r(1767),u=r(9462),l=r(6319),f=r(9539),p=r(684),h=r(4549),d=r(6395),y=!d&&!p("map",function(){}),g=!d&&!y&&h("map",TypeError),m=d||y||g,b=u(function(){var e=this.iterator,t=a(o(this.next,e));if(!(this.done=!!t.done))return l(e,this.mapper,[t.value,this.counter++],!0)});i({target:"Iterator",proto:!0,real:!0,forced:m},{map:function(e){a(this);try{s(e)}catch(e){f(this,"throw",e)}return g?o(g,this,e):new b(c(this),{mapper:e})}})},1750:(e,t,r)=>{"use strict";var i=r(9565),o=r(9306),s=r(8551),a=r(34),c=r(1767),u=r(2059),l=r(2529),f=r(772),p=u(function(e){var t=this,r=t.iterator,o=t.mapper;return new e(function(c,u){var p=function(e){t.done=!0,u(e)},h=function(e){f(r,p,e,p)};e.resolve(s(i(t.next,r))).then(function(r){try{if(s(r).done)t.done=!0,c(l(void 0,!0));else{var i=r.value;try{var u=o(i,t.counter++),f=function(e){c(l(e,!1))};a(u)?e.resolve(u).then(f,h):f(u)}catch(e){h(e)}}}catch(e){p(e)}},p)})});e.exports=function(e){return s(this),o(e),new p(c(this),{mapper:e})}},1753:e=>{"use strict";e.exports=/[^.[\]]+|\[((?:.)*?)\]/g},1767:e=>{"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},1809:(e,t,r)=>{"use strict";let{isColorSupported:i}=r(5844),o=r(815),{Transform:s}=r(4198),a=r(2398),c=r(2699),u=r(7589),{ERROR_LIKE_KEYS:l,MESSAGE_KEY:f,TIMESTAMP_KEY:p,LEVEL_KEY:h,LEVEL_NAMES:d}=r(4160),{isObject:y,prettifyErrorLog:g,prettifyLevel:m,prettifyMessage:b,prettifyMetadata:v,prettifyObject:w,prettifyTime:_,buildSafeSonicBoom:S,filterLog:x}=r(324),E={colorize:i,crlf:!1,errorLikeObjectKeys:l,errorProps:"",customLevels:null,customColors:null,useOnlyCustomProps:!0,levelFirst:!1,messageKey:f,messageFormat:!1,timestampKey:p,translateTime:!1,useMetadata:!1,outputStream:process.stdout,customPrettifiers:{},hideObject:!1,singleLine:!1};function O(e){let t=Object.assign({},E,e),r=t.crlf?`\r
`:`
`,i="    ",o=t.messageKey,s=t.levelKey,a=t.levelLabel,l=t.minimumLevel,f=t.messageFormat,p=t.timestampKey,S=t.errorLikeObjectKeys,O=t.errorProps.split(","),k="boolean"==typeof t.useOnlyCustomProps?t.useOnlyCustomProps:"true"===t.useOnlyCustomProps,j=t.customLevels?t.customLevels.split(",").reduce((e,t,r)=>{let[i,o=r]=t.split(":");return e[o]=i.toUpperCase(),e},{default:"USERLVL"}):{},T=t.customLevels?t.customLevels.split(",").reduce((e,t,r)=>{let[i,o=r]=t.split(":");return e[i.toLowerCase()]=o,e},{}):{},R=t.customColors?t.customColors.split(",").reduce((e,r)=>{let[i,o]=r.split(":"),s=(k?t.customLevels:void 0!==T[i])?T[i]:d[i];return e.push([void 0!==s?s:i,o]),e},[]):void 0,P={customLevels:j,customLevelNames:T};k&&!t.customLevels&&(P.customLevels=void 0,P.customLevelNames=void 0);let L=t.customPrettifiers,M=t.ignore?new Set(t.ignore.split(",")):void 0,A=t.hideObject,N=t.singleLine,C=u(t.colorize,R,k);return function(e){let u;if(y(e))u=e;else{let t=(e=>{try{return{value:c.parse(e,{protoAction:"remove"})}}catch(e){return{err:e}}})(e);if(t.err||!y(t.value))return e+r;u=t.value}if(l){let e=((k?t.customLevels:void 0!==T[l])?T[l]:d[l])||Number(l);if(u[void 0===s?h:s]<e)return}let E=b({log:u,messageKey:o,colorizer:C,messageFormat:f,levelLabel:a,...P,useOnlyCustomProps:k});M&&(u=x(u,M));let j=m({log:u,colorizer:C,levelKey:s,prettifier:L.level,...P}),R=v({log:u,prettifiers:L}),I=_({log:u,translateFormat:t.translateTime,timestampKey:p,prettifier:L.time}),D="";if(t.levelFirst&&j&&(D=`${j}`),I&&""===D?D=`${I}`:I&&(D=`${D} ${I}`),!t.levelFirst&&j&&(D=D.length>0?`${D} ${j}`:j),R&&(D=D.length>0?`${D} ${R}:`:R),!1===D.endsWith(":")&&""!==D&&(D+=":"),E&&(D=D.length>0?`${D} ${E}`:E),D.length>0&&!N&&(D+=r),"Error"===u.type&&u.stack){let e=g({log:u,errorLikeKeys:S,errorProperties:O,ident:i,eol:r});N&&(D+=r),D+=e}else if(!A){let e=[o,s,p].filter(e=>"string"==typeof u[e]||"number"==typeof u[e]),t=w({input:u,skipKeys:e,customPrettifiers:L,errorLikeKeys:S,eol:r,ident:i,singleLine:N,colorizer:C});N&&!/^\s$/.test(t)&&(D+=" "),D+=t}return D}}function k(e={}){let t=O(e);return a(function(r){let i,a=new s({objectMode:!0,autoDestroy:!0,transform(e,r,i){i(null,t(e))}});return i="object"==typeof e.destination&&"function"==typeof e.destination.write?e.destination:S({dest:e.destination||1,append:e.append,mkdir:e.mkdir,sync:e.sync}),r.on("unknown",function(e){i.write(e+`
`)}),o(r,a,i),a},{parse:"lines"})}e.exports=k,e.exports.prettyFactory=O,e.exports.colorizerFactory=u,e.exports.default=k},1828:(e,t,r)=>{"use strict";var i=r(9504),o=r(9297),s=r(5397),a=r(9617).indexOf,c=r(421),u=i([].push);e.exports=function(e,t){var r,i=s(e),l=0,f=[];for(r in i)!o(c,r)&&o(i,r)&&u(f,r);for(;t.length>l;)o(i,r=t[l++])&&(~a(f,r)||u(f,r));return f}},1926:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(7080),a=r(8469);i({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0);return!0===a(t,function(e){if(r(e,e,t))return!0},!0)}})},1927:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(7080),a=r(8469);i({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0);return!1!==a(t,function(e){if(!r(e,e,t))return!1},!0)}})},2017:(e,t,r)=>{try{var i=r(9023);if("function"!=typeof i.inherits)throw"";e.exports=i.inherits}catch(t){e.exports=r(6698)}},2018:e=>{"use strict";e.exports=require("tty")},2059:(e,t,r)=>{"use strict";var i=r(9565),o=r(1103),s=r(8551),a=r(2360),c=r(6699),u=r(6279),l=r(8227),f=r(1181),p=r(7751),h=r(5966),d=r(3982),y=r(2529),g=r(9539),m=p("Promise"),b=l("toStringTag"),v="AsyncIteratorHelper",w="WrapForValidAsyncIterator",_=f.set,S=function(e){var t=!e,r=f.getterFor(e?w:v),c=function(e){var i=o(function(){return r(e)}),s=i.error,a=i.value;return s||t&&a.done?{exit:!0,value:s?m.reject(a):m.resolve(y(void 0,!0))}:{exit:!1,value:a}};return u(a(d),{next:function(){var e=c(this),t=e.value;if(e.exit)return t;var r=o(function(){return s(t.nextHandler(m))}),i=r.error,a=r.value;return i&&(t.done=!0),i?m.reject(a):m.resolve(a)},return:function(){var t,r,a=c(this),u=a.value;if(a.exit)return u;u.done=!0;var l=u.iterator,f=o(function(){if(u.inner)try{g(u.inner.iterator,"normal")}catch(e){return g(l,"throw",e)}return h(l,"return")});return(t=r=f.value,f.error)?m.reject(r):void 0===t?m.resolve(y(void 0,!0)):(r=(f=o(function(){return i(t,l)})).value,f.error)?m.reject(r):e?m.resolve(r):m.resolve(r).then(function(e){return s(e),y(void 0,!0)})}})},x=S(!0),E=S(!1);c(E,b,"Async Iterator Helper"),e.exports=function(e,t){var r=function(r,i){i?(i.iterator=r.iterator,i.next=r.next):i=r,i.type=t?w:v,i.nextHandler=e,i.counter=0,i.done=!1,_(this,i)};return r.prototype=t?x:E,r}},2106:(e,t,r)=>{"use strict";var i=r(283),o=r(4913);e.exports=function(e,t,r){return r.get&&i(r.get,t,{getter:!0}),r.set&&i(r.set,t,{setter:!0}),o.f(e,t,r)}},2140:(e,t,r)=>{"use strict";var i=r(8227)("toStringTag"),o={};o[i]="z",e.exports="[object z]"===String(o)},2141:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e){super("The response of youtube-dl is malformed."),this.name="YoutubeDlInvalidResponse",this.response=e}}},2187:(e,t,r)=>{"use strict";let{groupRestore:i,nestedRestore:o}=r(7157);e.exports=function(){return function(){var e;if(this.restore){this.restore.state.secret=this.secret;return}let{secret:t,wcLen:r}=this,s=Object.keys(t),a=(e=t,s.map(t=>{let{circle:r,escPath:i,leadingBracket:o}=e[t],s=r?`o.${r} = secret[${i}].val`:`o${o?"":"."}${t} = secret[${i}].val`,a=`secret[${i}].val = undefined`;return`
      if (secret[${i}].val !== undefined) {
        try { ${s} } catch (e) {}
        ${a}
      }
    `}).join("")),c=r>0,u=c?{secret:t,groupRestore:i,nestedRestore:o}:{secret:t};this.restore=Function("o",function(e,t,r){let i=!0===r?`
    const keys = Object.keys(secret)
    const len = keys.length
    for (var i = len - 1; i >= ${t.length}; i--) {
      const k = keys[i]
      const o = secret[k]
      if (o) {
        if (o.flat === true) this.groupRestore(o)
        else this.nestedRestore(o)
        secret[k] = null
      }
    }
  `:"";return`
    const secret = this.secret
    ${i}
    ${e}
    return o
  `}(a,s,c)).bind(u),this.restore.state=u}}},2195:(e,t,r)=>{"use strict";var i=r(9504),o=i({}.toString),s=i("".slice);e.exports=function(e){return s(o(e),8,-1)}},2203:e=>{"use strict";e.exports=require("stream")},2211:(e,t,r)=>{"use strict";e.exports=!r(9039)(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2248:(e,t,r)=>{"use strict";var i=r(9504),o=Map.prototype;e.exports={Map:Map,set:i(o.set),get:i(o.get),has:i(o.has),remove:i(o.delete),proto:o}},2250:e=>{"use strict";e.exports=require("dns")},2359:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=a(t,function(e,i){if(r(e,i,t))return{value:e}},!0);return i&&i.value}})},2360:(e,t,r)=>{"use strict";var i,o=r(8551),s=r(6801),a=r(8727),c=r(421),u=r(397),l=r(4055),f=r(6119),p="prototype",h="script",d=f("IE_PROTO"),y=function(){},g=function(e){return"<"+h+">"+e+"</"+h+">"},m=function(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t},b=function(){var e,t=l("iframe");return t.style.display="none",u.appendChild(t),t.src=String("java"+h+":"),(e=t.contentWindow.document).open(),e.write(g("document.F=Object")),e.close(),e.F},v=function(){try{i=new ActiveXObject("htmlfile")}catch(e){}v="undefined"!=typeof document?document.domain&&i?m(i):b():m(i);for(var e=a.length;e--;)delete v[p][a[e]];return v()};c[d]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(y[p]=o(e),r=new y,y[p]=null,r[d]=e):r=v(),void 0===t?r:s.f(r,t)}},2398:(e,t,r)=>{"use strict";let i=Symbol.for("pino.metadata"),o=r(4615),s=r(2623);function a(e,t){process.nextTick(t,e)}e.exports=function(e,t={}){let r="lines"===t.parse,c="function"==typeof t.parseLine?t.parseLine:JSON.parse,u=t.close||a,l=o(function(e){let t;try{t=c(e)}catch(t){this.emit("unknown",e,t);return}return null===t?void this.emit("unknown",e,"Null value ignored"):("object"!=typeof t&&(t={data:t,time:Date.now()}),l[i]&&(l.lastTime=t.time,l.lastLevel=t.level,l.lastObj=t),r)?e:t},{autoDestroy:!0});l._destroy=function(e,t){let r=u(e,t);r&&"function"==typeof r.then&&r.then(t,t)},!1!==t.metadata&&(l[i]=!0,l.lastTime=0,l.lastLevel=0,l.lastObj=null);let f=e(l);if(f&&"function"==typeof f.catch)f.catch(e=>{l.destroy(e)}),f=null;else if(t.enablePipelining&&f)return s(l,f,{objectMode:!0});return l}},2489:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(8551),c=r(1767),u=r(9462),l=r(6319),f=r(6395),p=r(9539),h=r(684),d=r(4549),y=!f&&!h("filter",function(){}),g=!f&&!y&&d("filter",TypeError),m=f||y||g,b=u(function(){for(var e,t,r=this.iterator,i=this.predicate,s=this.next;;){if(e=a(o(s,r)),this.done=!!e.done)return;if(l(r,i,[t=e.value,this.counter++],!0))return t}});i({target:"Iterator",proto:!0,real:!0,forced:m},{filter:function(e){a(this);try{s(e)}catch(e){p(this,"throw",e)}return g?o(g,this,e):new b(c(this),{predicate:e})}})},2503:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4114),r(1393),r(1454),r(7333),r(4520),r(8992),r(9920),r(3949),r(9479),r(4905),r(8872);let i={width:80,_program:{},_options:[],program:(e={})=>(i._program=e,i),option:(e,t={})=>(e=Array.isArray(e)?e:[e],t.dest=t.dest||e.slice(-1)[0].toLowerCase().replace(/^-+/,"").replace(/-[a-z]/g,e=>e.slice(1).toUpperCase()),t.help=t.help||({help:"output usage information",version:"output the version number"})[t.action],i._options.push(Object.assign(t,{flags:e,positional:!e[0].startsWith("-")})),i),parse:e=>{let t=i._options.map((e,t)=>e.positional?t:null).filter(e=>null!==e),o={};i._options.forEach((e,t)=>e.positional?null:e.flags.forEach(e=>o[e]=t)),i._program.name=i._program.name||r(6928).parse(e[1]).base;let s=e.slice(2).reduce((e,t)=>/^-[^-]/.test(t)?e.concat(t.slice(1).split("").map(e=>"-"+e)):e.concat(t),[]),l=0;for(;l<s.length;){let e=null,r=s[l],f=r.startsWith("-")?o[r]:t.shift();void 0===f&&(r.startsWith("-")?u(`no such option: ${r}`):u(`extra arguments found: ${r}`)),r.startsWith("-")&&(l+=1);let{action:p}=i._options[f];if(["help","version"].includes(p))"help"===p?a():"version"===p&&c();else if(["store_true","store_false"].includes(p))e="store_true"===p;else{let t=s.slice(l).findIndex(e=>e in o),a=-1===t?s.length:l+t;0===(e=s.slice(l,a)).length&&(i._options[f].positional?u(`the following arguments are required: ${r}`):"+"===i._options[f].nargs?u(`argument ${r}: expected at least one argument`):u(`argument ${r}: expected one argument`)),"+"!==i._options[f].nargs?(e=e[0],l+=1):l=a}i[i._options[f].dest]=e}return t.length&&u(`the following arguments are required: ${t.map(e=>i._options[e].flags[0]).join(", ")}`),i}},o=e=>Array(e+1).join(" "),s=()=>{let e=i._options.map(e=>{let t=e.flags.sort((e,t)=>e.length-t.length)[0],r=e.metavar||e.dest;if(e.positional)if("+"===e.nargs)return`${r} [${r} ...]`;else return`${r}`;return["store_true","store_false","help","version"].includes(e.action)?`[${t}]`:"+"===e.nargs?`[${t} ${r} [${r} ...]]`:`[${t} ${r}]`}),t=i.width,r=`usage: ${i._program.name}`,s=[r];e.map(e=>" "+e).forEach(e=>{s[s.length-1].length+e.length<t?s[s.length-1]+=e:s.push(o(r.length)+e)}),console.log(s.join(`
`))},a=()=>{s();let e=i._options.filter(e=>e.positional).map(e=>[e.metavar||e.dest,e.help]),t=i._options.filter(e=>!e.positional).map(e=>{let{flags:t}=e,r=e.metavar||e.dest;return[["store_true","store_false","help","version"].includes(e.action)?t.map(e=>`${e}`).join(", "):"+"===e.nargs?t.map(e=>`${e} ${r} [${r} ...]`).join(", "):t.map(e=>`${e} ${r}`).join(", "),e.help]}),r=Math.max.apply(null,e.concat(t).map(e=>e[0].length));r=r>30?30:r;let a=i.width-r-4,c=e=>{let t=e=>Array.from(Array(Math.ceil(e.length/a)).keys()).map(t=>e.slice(t*a,(t+1)*a)).join(`
`+o(r+4));e[0].length<r?console.log(`  ${e[0]}${o(r-e[0].length)}  ${t(e[1])}`):console.log(`  ${e[0]}
${o(r+4)}${t(e[1])}`)};e.length&&console.log(`
positional arguments:`),e.forEach(c),t.length&&console.log(`
optional arguments:`),t.forEach(c),process.exit()},c=()=>{console.log(i._program.version),process.exit()},u=e=>{s(),console.log(i._program.name+":","error:",e),process.exit(1)};e.exports=i},2514:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(8527);i({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){return o(a,this,s(e))}})},2516:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(4449);i({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){return o(a,this,s(e))}})},2529:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},2569:(e,t,r)=>{"use strict";let i=r(9664);e.exports=()=>{let t=global.cnrelay,r=new Proxy(()=>{},{get:(e,t)=>(e.route=(e.route||[]).concat(t),r),apply:(r,o,s)=>{if(e.exports.disable||!t)return Promise.reject();let a=r.route.join("/"),c="object"==typeof s[0]?JSON.stringify(s[0]):s[0];return i("GET",`${t}/${a}?${encodeURIComponent(c)}`).then(e=>e.body())}});return r}},2577:(e,t,r)=>{"use strict";r(116)},2603:(e,t,r)=>{"use strict";var i=r(655);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:i(e)}},2623:(e,t,r)=>{var i=r(4198),o=r(6611),s=r(2017),a=r(5332),c=Buffer.from&&Buffer.from!==Uint8Array.from?Buffer.from([0]):new Buffer([0]),u=function(e,t){e._corked?e.once("uncork",t):t()},l=function(e,t){e._autoDestroy&&e.destroy(t)},f=function(e,t){return function(r){r?l(e,"premature close"===r.message?null:r):t&&!e._ended&&e.end()}},p=function(){},h=function(e,t,r){if(!(this instanceof h))return new h(e,t,r);i.Duplex.call(this,r),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!r||!1!==r.autoDestroy,this._forwardDestroy=!r||!1!==r.destroy,this._forwardEnd=!r||!1!==r.end,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,e&&this.setWritable(e),t&&this.setReadable(t)};s(h,i.Duplex),h.obj=function(e,t,r){return r||(r={}),r.objectMode=!0,r.highWaterMark=16,new h(e,t,r)},h.prototype.cork=function(){1==++this._corked&&this.emit("cork")},h.prototype.uncork=function(){this._corked&&0==--this._corked&&this.emit("uncork")},h.prototype.setWritable=function(e){if(this._unwrite&&this._unwrite(),this.destroyed){e&&e.destroy&&e.destroy();return}if(null===e||!1===e)return void this.end();var t=this,r=o(e,{writable:!0,readable:!1},f(this,this._forwardEnd)),i=function(){var e=t._ondrain;t._ondrain=null,e&&e()};this._unwrite&&process.nextTick(i),this._writable=e,this._writable.on("drain",i),this._unwrite=function(){t._writable.removeListener("drain",i),r()},this.uncork()},h.prototype.setReadable=function(e){if(this._unread&&this._unread(),this.destroyed){e&&e.destroy&&e.destroy();return}if(null===e||!1===e){this.push(null),this.resume();return}var t=this,r=o(e,{writable:!1,readable:!0},f(this)),s=function(){t._forward()},a=function(){t.push(null)};this._drained=!0,this._readable=e,this._readable2=e._readableState?e:new i.Readable({objectMode:!0,highWaterMark:16}).wrap(e),this._readable2.on("readable",s),this._readable2.on("end",a),this._unread=function(){t._readable2.removeListener("readable",s),t._readable2.removeListener("end",a),r()},this._forward()},h.prototype._read=function(){this._drained=!0,this._forward()},h.prototype._forward=function(){var e;if(!this._forwarding&&this._readable2&&this._drained){for(this._forwarding=!0;this._drained&&null!==(e=a(this._readable2));)this.destroyed||(this._drained=this.push(e));this._forwarding=!1}},h.prototype.destroy=function(e,t){if(t||(t=p),this.destroyed)return t(null);this.destroyed=!0;var r=this;process.nextTick(function(){r._destroy(e),t(null)})},h.prototype._destroy=function(e){if(e){var t=this._ondrain;this._ondrain=null,t?t(e):this.emit("error",e)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},h.prototype._write=function(e,t,r){if(!this.destroyed){if(this._corked)return u(this,this._write.bind(this,e,t,r));if(e===c)return this._finish(r);if(!this._writable)return r();!1===this._writable.write(e)?this._ondrain=r:this.destroyed||r()}},h.prototype._finish=function(e){var t=this;this.emit("preend"),u(this,function(){var r,i;r=t._forwardEnd&&t._writable,i=function(){!1===t._writableState.prefinished&&(t._writableState.prefinished=!0),t.emit("prefinish"),u(t,e)},!r||r._writableState&&r._writableState.finished?i():r._writableState?r.end(i):(r.end(),i())})},h.prototype.end=function(e,t,r){return"function"==typeof e?this.end(null,null,e):"function"==typeof t?this.end(e,null,t):(this._ended=!0,e&&this.write(e),this._writableState.ending||this._writableState.destroyed||this.write(c),i.Writable.prototype.end.call(this,r))},e.exports=h},2651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e,t="?"){super(`This song "${t}" is not available in ${e}`),this.name="SongNotAvailable"}}},2652:(e,t,r)=>{"use strict";var i=r(6080),o=r(9565),s=r(8551),a=r(6823),c=r(4209),u=r(6198),l=r(1625),f=r(81),p=r(851),h=r(9539),d=TypeError,y=function(e,t){this.stopped=e,this.result=t},g=y.prototype;e.exports=function(e,t,r){var m,b,v,w,_,S,x,E=r&&r.that,O=!!(r&&r.AS_ENTRIES),k=!!(r&&r.IS_RECORD),j=!!(r&&r.IS_ITERATOR),T=!!(r&&r.INTERRUPTED),R=i(t,E),P=function(e){return m&&h(m,"normal"),new y(!0,e)},L=function(e){return O?(s(e),T?R(e[0],e[1],P):R(e[0],e[1])):T?R(e,P):R(e)};if(k)m=e.iterator;else if(j)m=e;else{if(!(b=p(e)))throw new d(a(e)+" is not iterable");if(c(b)){for(v=0,w=u(e);w>v;v++)if((_=L(e[v]))&&l(g,_))return _;return new y(!1)}m=f(e,b)}for(S=k?e.next:m.next;!(x=o(S,m)).done;){try{_=L(x.value)}catch(e){h(m,"throw",e)}if("object"==typeof _&&_&&l(g,_))return _}return new y(!1)}},2699:e=>{"use strict";let t="undefined"!=typeof Buffer,r=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,i=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function o(e,o,a){null==a&&null!==o&&"object"==typeof o&&(a=o,o=void 0),t&&Buffer.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));let c=JSON.parse(e,o);if(null===c||"object"!=typeof c)return c;let u=a&&a.protoAction||"error",l=a&&a.constructorAction||"error";if("ignore"===u&&"ignore"===l)return c;if("ignore"!==u&&"ignore"!==l){if(!1===r.test(e)&&!1===i.test(e))return c}else if("ignore"!==u&&"ignore"===l){if(!1===r.test(e))return c}else if(!1===i.test(e))return c;return s(c,{protoAction:u,constructorAction:l,safe:a&&a.safe})}function s(e,{protoAction:t="error",constructorAction:r="error",safe:i}={}){let o=[e];for(;o.length;){let e=o;for(let s of(o=[],e)){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(s,"__proto__")){if(!0===i)return null;if("error"===t)throw SyntaxError("Object contains forbidden prototype property");delete s.__proto__}if("ignore"!==r&&Object.prototype.hasOwnProperty.call(s,"constructor")&&Object.prototype.hasOwnProperty.call(s.constructor,"prototype")){if(!0===i)return null;if("error"===r)throw SyntaxError("Object contains forbidden prototype property");delete s.constructor}for(let e in s){let t=s[e];t&&"object"==typeof t&&o.push(t)}}}return e}function a(e,t,r){let i=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return o(e,t,r)}finally{Error.stackTraceLimit=i}}e.exports=a,e.exports.default=a,e.exports.parse=a,e.exports.safeParse=function(e,t){let r=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return o(e,t,{safe:!0})}catch(e){return null}finally{Error.stackTraceLimit=r}},e.exports.scan=s},2774:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(7080),a=r(4402),c=r(8469),u=a.Set,l=a.add;i({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=new u;return c(t,function(e){l(i,r(e,e,t))}),i}})},2777:(e,t,r)=>{"use strict";var i=r(9565),o=r(34),s=r(757),a=r(5966),c=r(4270),u=r(8227),l=TypeError,f=u("toPrimitive");e.exports=function(e,t){if(!o(e)||s(e))return e;var r,u=a(e,f);if(u){if(void 0===t&&(t="default"),!o(r=i(u,e,t))||s(r))return r;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),c(e,t)}},2787:(e,t,r)=>{"use strict";var i=r(9297),o=r(4901),s=r(8981),a=r(6119),c=r(2211),u=a("IE_PROTO"),l=Object,f=l.prototype;e.exports=c?l.getPrototypeOf:function(e){var t=s(e);if(i(t,u))return t[u];var r=t.constructor;return o(r)&&t instanceof r?r.prototype:t instanceof l?f:null}},2796:(e,t,r)=>{"use strict";var i=r(9039),o=r(4901),s=/#|\.prototype\./,a=function(e,t){var r=u[c(e)];return r===f||r!==l&&(o(t)?i(t):!!t)},c=a.normalize=function(e){return String(e).replace(s,".").toLowerCase()},u=a.data={},l=a.NATIVE="N",f=a.POLYFILL="P";e.exports=a},2839:(e,t,r)=>{"use strict";var i=r(4576).navigator,o=i&&i.userAgent;e.exports=o?String(o):""},2860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(){super('You must install "youtube-dl" before using the "youtubedl" source.'),this.name="YoutubeDlNotInstalled"}}},2861:(e,t,r)=>{var i=r(181),o=i.Buffer;function s(e,t){for(var r in e)t[r]=e[r]}function a(e,t,r){return o(e,t,r)}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?e.exports=i:(s(i,t),t.Buffer=a),a.prototype=Object.create(o.prototype),s(o,a),a.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return o(e,t,r)},a.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=o(e);return void 0!==t?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i},a.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return o(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},2955:(e,t,r)=>{"use strict";function i(e,t,r){var i;return(t="symbol"==typeof(i=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?i:String(i))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var o,s=r(6238),a=Symbol("lastResolve"),c=Symbol("lastReject"),u=Symbol("error"),l=Symbol("ended"),f=Symbol("lastPromise"),p=Symbol("handlePromise"),h=Symbol("stream");function d(e,t){return{value:e,done:t}}function y(e){var t=e[a];if(null!==t){var r=e[h].read();null!==r&&(e[f]=null,e[a]=null,e[c]=null,t(d(r,!1)))}}function g(e){process.nextTick(y,e)}var m=Object.getPrototypeOf(function(){}),b=Object.setPrototypeOf((i(o={get stream(){return this[h]},next:function(){var e,t,r=this,i=this[u];if(null!==i)return Promise.reject(i);if(this[l])return Promise.resolve(d(void 0,!0));if(this[h].destroyed)return new Promise(function(e,t){process.nextTick(function(){r[u]?t(r[u]):e(d(void 0,!0))})});var o=this[f];if(o)t=new Promise((e=this,function(t,r){o.then(function(){if(e[l])return void t(d(void 0,!0));e[p](t,r)},r)}));else{var s=this[h].read();if(null!==s)return Promise.resolve(d(s,!1));t=new Promise(this[p])}return this[f]=t,t}},Symbol.asyncIterator,function(){return this}),i(o,"return",function(){var e=this;return new Promise(function(t,r){e[h].destroy(null,function(e){if(e)return void r(e);t(d(void 0,!0))})})}),o),m);e.exports=function(e){var t,r=Object.create(b,(i(t={},h,{value:e,writable:!0}),i(t,a,{value:null,writable:!0}),i(t,c,{value:null,writable:!0}),i(t,u,{value:null,writable:!0}),i(t,l,{value:e._readableState.endEmitted,writable:!0}),i(t,p,{value:function(e,t){var i=r[h].read();i?(r[f]=null,r[a]=null,r[c]=null,e(d(i,!1))):(r[a]=e,r[c]=t)},writable:!0}),t));return r[f]=null,s(e,function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=r[c];null!==t&&(r[f]=null,r[a]=null,r[c]=null,t(e)),r[u]=e;return}var i=r[a];null!==i&&(r[f]=null,r[a]=null,r[c]=null,i(d(void 0,!0))),r[l]=!0}),e.on("readable",g.bind(null,r)),r}},2967:(e,t,r)=>{"use strict";var i=r(6706),o=r(34),s=r(7750),a=r(3506);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=i(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,i){return s(r),a(i),o(r)&&(t?e(r,i):r.__proto__=i),r}}():void 0)},3064:(e,t,r)=>{"use strict";var i=r(6518),o=r(6639).find;i({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{find:function(e){return o(this,e)}})},3106:e=>{"use strict";e.exports=require("zlib")},3125:(e,t,r)=>{"use strict";let i=r(857),o=r(6437),s=r(8624),a=r(4216),c=r(8687),u=r(544),{assertDefaultLevelFound:l,mappings:f,genLsCache:p}=r(754),{createArgsNormalizer:h,asChindings:d,final:y,stringify:g,buildSafeSonicBoom:m,buildFormatters:b,noop:v}=r(6526),{version:w}=r(5660),{mixinMergeStrategySym:_}=r(544),{chindingsSym:S,redactFmtSym:x,serializersSym:E,timeSym:O,timeSliceIndexSym:k,streamSym:j,stringifySym:T,stringifiersSym:R,setLevelSym:P,endSym:L,formatOptsSym:M,messageKeySym:A,nestedKeySym:N,mixinSym:C,useOnlyCustomLevelsSym:I,formattersSym:D,hooksSym:B}=u,{epochTime:$,nullTime:q}=a,{pid:U}=process,F=i.hostname(),W=o.err,G={level:"info",messageKey:"msg",nestedKey:null,enabled:!0,prettyPrint:!1,base:{pid:U,hostname:F},serializers:Object.assign(Object.create(null),{err:W}),formatters:Object.assign(Object.create(null),{bindings:e=>e,level:(e,t)=>({level:t})}),hooks:{logMethod:void 0},timestamp:$,name:void 0,redact:null,customLevels:null,levelKey:void 0,useOnlyCustomLevels:!1},H=h(G),z=Object.assign(Object.create(null),o);function J(...e){var t,r;let i={},{opts:o,stream:a}=H(i,...e),{redact:u,crlf:h,serializers:y,timestamp:m,messageKey:w,nestedKey:U,base:F,name:W,level:z,customLevels:V,useLevelLabels:Y,changeLevelName:Q,levelKey:X,mixin:Z,mixinMergeStrategy:ee,useOnlyCustomLevels:et,formatters:er,hooks:en}=o,ei=b(er.level,er.bindings,er.log);Y&&!(Q||X)?(process.emitWarning("useLevelLabels is deprecated, use the formatters.level option instead","Warning","PINODEP001"),ei.level=K):(Q||X)&&!Y?(process.emitWarning("changeLevelName and levelKey are deprecated, use the formatters.level option instead","Warning","PINODEP002"),t=Q||X,ei.level=function(e,r){return{[t]:r}}):(Q||X)&&Y&&(process.emitWarning("useLevelLabels is deprecated, use the formatters.level option instead","Warning","PINODEP001"),process.emitWarning("changeLevelName and levelKey are deprecated, use the formatters.level option instead","Warning","PINODEP002"),r=Q||X,ei.level=function(e,t){return{[r]:e}}),y[Symbol.for("pino.*")]&&(process.emitWarning("The pino.* serializer is deprecated, use the formatters.log options instead","Warning","PINODEP003"),ei.log=y[Symbol.for("pino.*")]),ei.bindings||(ei.bindings=G.formatters.bindings),ei.level||(ei.level=G.formatters.level);let eo=u?s(u,g):{},es=u?{stringify:eo[x]}:{stringify:g},ea="}"+(h?`\r
`:`
`),ec=d.bind(null,{[S]:"",[E]:y,[R]:eo,[T]:g,[D]:ei}),eu="";null!==F&&(eu=ec(void 0===W?F:Object.assign({},F,{name:W})));let el=m instanceof Function?m:m?$:q,ef=el().indexOf(":")+1;if(et&&!V)throw Error("customLevels is required if useOnlyCustomLevels is set true");if(Z&&"function"!=typeof Z)throw Error(`Unknown mixin type "${typeof Z}" - expected "function"`);return l(z,V,et),Object.assign(i,{levels:f(V,et),[I]:et,[j]:a,[O]:el,[k]:ef,[T]:g,[R]:eo,[L]:ea,[M]:es,[A]:w,[N]:U,[E]:y,[C]:Z,[_]:ee,[S]:eu,[D]:ei,[B]:en,silent:v}),Object.setPrototypeOf(i,c()),p(i),i[P](z),i}function K(e,t){return{level:e}}e.exports=J,e.exports.extreme=(e=process.stdout.fd)=>(process.emitWarning("The pino.extreme() option is deprecated and will be removed in v7. Use pino.destination({ sync: false }) instead.",{code:"extreme_deprecation"}),m({dest:e,minLength:4096,sync:!1})),e.exports.destination=(e=process.stdout.fd)=>"object"==typeof e?(e.dest=e.dest||process.stdout.fd,m(e)):m({dest:e,minLength:0,sync:!0}),e.exports.final=y,e.exports.levels=f(),e.exports.stdSerializers=z,e.exports.stdTimeFunctions=Object.assign({},a),e.exports.symbols=u,e.exports.version=w,e.exports.default=J,e.exports.pino=J},3141:(e,t,r)=>{"use strict";var i=r(2861).Buffer,o=i.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function s(e){var t;switch(this.encoding=function(e){var t=function(e){var t;if(!e)return"utf8";for(;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(i.isEncoding===o||!o(e)))throw Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=u,this.end=l,t=4;break;case"utf8":this.fillLast=c,t=4;break;case"base64":this.text=f,this.end=p,t=3;break;default:this.write=h,this.end=d;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=i.allocUnsafe(t)}function a(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function c(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if((192&t[0])!=128)return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if((192&t[1])!=128)return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&(192&t[2])!=128)return e.lastNeed=2,"�"}}(this,e,0);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):void(e.copy(this.lastChar,t,0,e.length),this.lastNeed-=e.length)}function u(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function l(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function f(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function p(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function d(e){return e&&e.length?this.write(e):""}t.I=s,s.prototype.write=function(e){var t,r;if(0===e.length)return"";if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},s.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},s.prototype.text=function(e,t){var r=function(e,t,r){var i=t.length-1;if(i<r)return 0;var o=a(t[i]);return o>=0?(o>0&&(e.lastNeed=o-1),o):--i<r||-2===o?0:(o=a(t[i]))>=0?(o>0&&(e.lastNeed=o-2),o):--i<r||-2===o?0:(o=a(t[i]))>=0?(o>0&&(2===o?o=0:e.lastNeed=o-3),o):0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var i=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,i),e.toString("utf8",t,i)},s.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},3167:(e,t,r)=>{"use strict";var i=r(4901),o=r(34),s=r(2967);e.exports=function(e,t,r){var a,c;return s&&i(a=t.constructor)&&a!==r&&o(c=a.prototype)&&c!==r.prototype&&s(e,c),e}},3193:e=>{"use strict";e.exports=require("string_decoder")},3288:(e,t,r)=>{"use strict";let i=r(1753);e.exports=function({paths:e}){let t=[];var r=0;let o=e.reduce(function(e,o,s){var a=o.match(i).map(e=>e.replace(/'|"|`/g,""));let c="["===o[0],u=(a=a.map(e=>"["===e[0]?e.substr(1,e.length-2):e)).indexOf("*");if(u>-1){let e=a.slice(0,u),i=e.join("."),o=a.slice(u+1,a.length),s=o.length>0;r++,t.push({before:e,beforeStr:i,after:o,nested:s})}else e[o]={path:a,val:void 0,precensored:!1,circle:"",escPath:JSON.stringify(o),leadingBracket:c};return e},{});return{wildcards:t,wcLen:r,secret:o}}},3317:e=>{"use strict";e.exports=function(e,t){return e===t||e!=e&&t!=t}},3392:(e,t,r)=>{"use strict";var i=r(9504),o=0,s=Math.random(),a=i(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+s,36)}},3440:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402),s=r(9286),a=r(5170),c=r(3789),u=r(8469),l=r(507),f=o.has,p=o.remove;e.exports=function(e){var t=i(this),r=c(e),o=s(t);return a(t)<=r.size?u(t,function(e){r.includes(e)&&p(o,e)}):l(r.getIterator(),function(e){f(o,e)&&p(o,e)}),o}},3480:e=>{"use strict";e.exports=require("querystring")},3506:(e,t,r)=>{"use strict";var i=r(3925),o=String,s=TypeError;e.exports=function(e){if(i(e))return e;throw new s("Can't set "+o(e)+" as a prototype")}},3518:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(7751),c=r(6043),u=r(1103),l=r(2652),f=r(537),p="No one promise resolved";i({target:"Promise",stat:!0,forced:f},{any:function(e){var t=this,r=a("AggregateError"),i=c.f(t),f=i.resolve,h=i.reject,d=u(function(){var i=s(t.resolve),a=[],c=0,u=1,d=!1;l(e,function(e){var s=c++,l=!1;u++,o(i,t,e).then(function(e){l||d||(d=!0,f(e))},function(e){!l&&!d&&(l=!0,a[s]=e,--u||h(new r(a,p)))})}),--u||h(new r(a,p))});return d.error&&h(d.value),i.promise}})},3519:(e,t,r)=>{var i=r(6587);function o(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function s(e){var t=function(){if(t.called)throw Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)};return t.onceError=(e.name||"Function wrapped with `once`")+" shouldn't be called more than once",t.called=!1,t}e.exports=i(o),e.exports.strict=i(s),o.proto=o(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return o(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return s(this)},configurable:!0})})},3550:e=>{"use strict";e.exports=JSON.parse('{"name":"pino","version":"6.14.0","description":"super fast, all natural json logger","main":"pino.js","browser":"./browser.js","files":["pino.js","bin.js","browser.js","pretty.js","usage.txt","test","docs","example.js","lib"],"scripts":{"docs":"docsify serve","browser-test":"airtap --local 8080 test/browser*test.js","lint":"eslint .","test":"npm run lint && tap --100 test/*test.js test/*/*test.js","test-ci":"npm run lint && tap test/*test.js test/*/*test.js --coverage-report=lcovonly","cov-ui":"tap --coverage-report=html test/*test.js test/*/*test.js","bench":"node benchmarks/utils/runbench all","bench-basic":"node benchmarks/utils/runbench basic","bench-object":"node benchmarks/utils/runbench object","bench-deep-object":"node benchmarks/utils/runbench deep-object","bench-multi-arg":"node benchmarks/utils/runbench multi-arg","bench-longs-tring":"node benchmarks/utils/runbench long-string","bench-child":"node benchmarks/utils/runbench child","bench-child-child":"node benchmarks/utils/runbench child-child","bench-child-creation":"node benchmarks/utils/runbench child-creation","bench-formatters":"node benchmarks/utils/runbench formatters","update-bench-doc":"node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"},"bin":{"pino":"./bin.js"},"precommit":"test","repository":{"type":"git","url":"git+https://github.com/pinojs/pino.git"},"keywords":["fast","logger","stream","json"],"author":"Matteo Collina <<EMAIL>>","contributors":["David Mark Clements <<EMAIL>>","James Sumners <<EMAIL>>","Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)"],"license":"MIT","bugs":{"url":"https://github.com/pinojs/pino/issues"},"homepage":"http://getpino.io","devDependencies":{"airtap":"4.0.3","benchmark":"^2.1.4","bole":"^4.0.0","bunyan":"^1.8.14","docsify-cli":"^4.4.1","eslint":"^7.17.0","eslint-config-standard":"^16.0.2","eslint-plugin-import":"^2.22.1","eslint-plugin-node":"^11.1.0","eslint-plugin-promise":"^5.1.0","execa":"^5.0.0","fastbench":"^1.0.1","flush-write-stream":"^2.0.0","import-fresh":"^3.2.1","log":"^6.0.0","loglevel":"^1.6.7","pino-pretty":"^5.0.0","pre-commit":"^1.2.2","proxyquire":"^2.1.3","pump":"^3.0.0","semver":"^7.0.0","split2":"^3.1.1","steed":"^1.1.3","strip-ansi":"^6.0.0","tap":"^15.0.1","tape":"^5.0.0","through2":"^4.0.0","winston":"^3.3.3"},"dependencies":{"fast-redact":"^3.0.0","fast-safe-stringify":"^2.0.8","process-warning":"^1.0.0","flatstr":"^1.0.12","pino-std-serializers":"^3.1.0","quick-format-unescaped":"^4.0.3","sonic-boom":"^1.0.2"}}')},3579:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(2652),a=r(9306),c=r(8551),u=r(1767),l=r(9539),f=r(4549)("some",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:f},{some:function(e){c(this);try{a(e)}catch(e){l(this,"throw",e)}if(f)return o(f,this,e);var t=u(this),r=0;return s(t,function(t,i){if(e(t,r++))return i()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3600:(e,t,r)=>{"use strict";e.exports=o;var i=r(4610);function o(e){if(!(this instanceof o))return new o(e);i.call(this,e)}r(2017)(o,i),o.prototype._transform=function(e,t,r){r(null,e)}},3650:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402),s=r(9286),a=r(3789),c=r(507),u=o.add,l=o.has,f=o.remove;e.exports=function(e){var t=i(this),r=a(e).getIterator(),o=s(t);return c(r,function(e){l(t,e)?f(o,e):u(o,e)}),o}},3706:(e,t,r)=>{"use strict";var i=r(9504),o=r(4901),s=r(7629),a=i(Function.toString);o(s.inspectSource)||(s.inspectSource=function(e){return a(e)}),e.exports=s.inspectSource},3717:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},3724:(e,t,r)=>{"use strict";e.exports=!r(9039)(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},3777:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0);return!1!==a(t,function(e,i){if(!r(e,i,t))return!1},!0)}})},3789:(e,t,r)=>{"use strict";var i=r(9306),o=r(8551),s=r(9565),a=r(1291),c=r(1767),u="Invalid size",l=RangeError,f=TypeError,p=Math.max,h=function(e,t){this.set=e,this.size=p(t,0),this.has=i(e.has),this.keys=i(e.keys)};h.prototype={getIterator:function(){return c(o(s(this.keys,this.set)))},includes:function(e){return s(this.has,this.set,e)}},e.exports=function(e){o(e);var t=+e.size;if(t!=t)throw new f(u);var r=a(t);if(r<0)throw new l(u);return new h(e,r)}},3838:(e,t,r)=>{"use strict";var i=r(7080),o=r(5170),s=r(8469),a=r(3789);e.exports=function(e){var t=i(this),r=a(e);return!(o(t)>r.size)&&!1!==s(t,function(e){if(!r.includes(e))return!1},!0)}},3922:(e,t,r)=>{"use strict";let i=r(9643),o=r(9664),{getManagedCacheStorage:s}=r(3971),a=s("provider/pyncmd");e.exports={check:e=>a.cache(e,()=>o("GET","https://music-api.gdstudio.xyz/api.php?types=url&source=netease&id="+e.id+"&br="+["999","320"].slice(+!i.ENABLE_FLAC,i.ENABLE_FLAC?1:2)).then(e=>e.json()).then(e=>e&&"object"==typeof e&&!1 in e?Promise.reject():e.br>0?e.url:Promise.reject()))}},3925:(e,t,r)=>{"use strict";var i=r(34);e.exports=function(e){return i(e)||null===e}},3949:(e,t,r)=>{"use strict";r(7588)},3971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1517),r(1379),r(3777),r(4190),r(2359),r(6097),r(7273),r(7415),r(9929),r(7583),r(5122),r(230),r(7268),r(9733),r(9920),r(3949),r(8992),r(5509),r(5223),r(321),r(1927),r(1632),r(4377),r(6771),r(2516),r(8931),r(2514),r(5694),r(2774),r(9536),r(1926),r(4483),r(6215);let{EventEmitter:i}=r(4434),{logScope:o}=r(5469),s=o("cache"),a={CLEANUP:"cs@cleanup"};class c extends i{id="Default Cache Storage";cacheMap=new Map;aliveDuration=18e5;constructor(e){super(),e&&(this.id=e),this.on(a.CLEANUP,async()=>this.removeExpiredCache())}get WillExpireAt(){return Date.now()+this.aliveDuration}getLoggerContext(e={}){return{...e,cacheStorageId:this.id}}removeExpiredCache(){s.debug(this.getLoggerContext(),"Cleaning up the expired caches..."),this.cacheMap.forEach((e,t)=>{e.expireAt<=Date.now()&&this.cacheMap.delete(t)})}async cache(e,t,r){if("true"===process.env.NO_CACHE)return t();this.emit(a.CLEANUP);let i=this.cacheMap.get(e),o="object"==typeof e?"Something":e,c=this.getLoggerContext({logKey:o});if(i)return s.debug(c,`${o} hit!`),i.data;s.debug(c,`${o} did not hit. Storing the execution result...`);let u=await t();return this.cacheMap.set(e,{data:u,expireAt:new Date(r||this.WillExpireAt)}),u}}class u{static instance=void 0;cacheStorages=new Set;constructor(){}static getInstance(){return u.instance||(u.instance=new u),u.instance}cleanup(){this.cacheStorages.forEach(e=>e.removeExpiredCache())}}let l=u.getInstance();e.exports={CacheStorage:c,CacheStorageEvents:a,CacheStorageGroup:u,getManagedCacheStorage:function(e){let t=new c(e);return l.cacheStorages.add(t),t}}},3982:(e,t,r)=>{"use strict";var i,o,s=r(4576),a=r(7629),c=r(4901),u=r(2360),l=r(2787),f=r(6840),p=r(8227),h=r(6395),d="USE_FUNCTION_CONSTRUCTOR",y=p("asyncIterator"),g=s.AsyncIterator,m=a.AsyncIteratorPrototype;if(m)i=m;else if(c(g))i=g.prototype;else if(a[d]||s[d])try{o=l(l(l(Function("return async function*(){}()")()))),l(o)===Object.prototype&&(i=o)}catch(e){}i?h&&(i=u(i)):i={},c(i[y])||f(i,y,function(){return this}),e.exports=i},4055:(e,t,r)=>{"use strict";var i=r(4576),o=r(34),s=i.document,a=o(s)&&o(s.createElement);e.exports=function(e){return a?s.createElement(e):{}}},4075:(e,t,r)=>{"use strict";var i;function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(s){var a,c,u,l=arguments,f=(a=/d{1,4}|D{3,4}|m{1,4}|yy(?:yy)?|([HhMsTt])\1?|W{1,2}|[LlopSZN]|"[^"]*"|'[^']*'/g,c=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,u=/[^-+\dA-Z]/g,function(e,t,r,i){if(1!==l.length||"string"!==g(e)||/\d/.test(e)||(t=e,e=void 0),(e=e||0===e?e:new Date)instanceof Date||(e=new Date(e)),isNaN(e))throw TypeError("Invalid date");var o=(t=String(f.masks[t]||t||f.masks.default)).slice(0,4);("UTC:"===o||"GMT:"===o)&&(t=t.slice(4),r=!0,"GMT:"===o&&(i=!0));var s=function(){return r?"getUTC":"get"},m=function(){return e[s()+"Date"]()},b=function(){return e[s()+"Day"]()},v=function(){return e[s()+"Month"]()},w=function(){return e[s()+"FullYear"]()},_=function(){return e[s()+"Hours"]()},S=function(){return e[s()+"Minutes"]()},x=function(){return e[s()+"Seconds"]()},E=function(){return e[s()+"Milliseconds"]()},O=function(){return r?0:e.getTimezoneOffset()},k=function(){return d(e)},j={d:function(){return m()},dd:function(){return p(m())},ddd:function(){return f.i18n.dayNames[b()]},DDD:function(){return h({y:w(),m:v(),d:m(),_:s(),dayName:f.i18n.dayNames[b()],short:!0})},dddd:function(){return f.i18n.dayNames[b()+7]},DDDD:function(){return h({y:w(),m:v(),d:m(),_:s(),dayName:f.i18n.dayNames[b()+7]})},m:function(){return v()+1},mm:function(){return p(v()+1)},mmm:function(){return f.i18n.monthNames[v()]},mmmm:function(){return f.i18n.monthNames[v()+12]},yy:function(){return String(w()).slice(2)},yyyy:function(){return p(w(),4)},h:function(){return _()%12||12},hh:function(){return p(_()%12||12)},H:function(){return _()},HH:function(){return p(_())},M:function(){return S()},MM:function(){return p(S())},s:function(){return x()},ss:function(){return p(x())},l:function(){return p(E(),3)},L:function(){return p(Math.floor(E()/10))},t:function(){return 12>_()?f.i18n.timeNames[0]:f.i18n.timeNames[1]},tt:function(){return 12>_()?f.i18n.timeNames[2]:f.i18n.timeNames[3]},T:function(){return 12>_()?f.i18n.timeNames[4]:f.i18n.timeNames[5]},TT:function(){return 12>_()?f.i18n.timeNames[6]:f.i18n.timeNames[7]},Z:function(){return i?"GMT":r?"UTC":(String(e).match(c)||[""]).pop().replace(u,"").replace(/GMT\+0000/g,"UTC")},o:function(){return(O()>0?"-":"+")+p(100*Math.floor(Math.abs(O())/60)+Math.abs(O())%60,4)},p:function(){return(O()>0?"-":"+")+p(Math.floor(Math.abs(O())/60),2)+":"+p(Math.floor(Math.abs(O())%60),2)},S:function(){return["th","st","nd","rd"][m()%10>3?0:(m()%100-m()%10!=10)*m()%10]},W:function(){return k()},WW:function(){return p(k())},N:function(){return y(e)}};return t.replace(a,function(e){return e in j?j[e]():e.slice(1,e.length-1)})});f.masks={default:"ddd mmm dd yyyy HH:MM:ss",shortDate:"m/d/yy",paddedShortDate:"mm/dd/yyyy",mediumDate:"mmm d, yyyy",longDate:"mmmm d, yyyy",fullDate:"dddd, mmmm d, yyyy",shortTime:"h:MM TT",mediumTime:"h:MM:ss TT",longTime:"h:MM:ss TT Z",isoDate:"yyyy-mm-dd",isoTime:"HH:MM:ss",isoDateTime:"yyyy-mm-dd'T'HH:MM:sso",isoUtcDateTime:"UTC:yyyy-mm-dd'T'HH:MM:ss'Z'",expiresHeaderFormat:"ddd, dd mmm yyyy HH:MM:ss Z"},f.i18n={dayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"],timeNames:["a","p","am","pm","A","P","AM","PM"]};var p=function(e,t){for(e=String(e),t=t||2;e.length<t;)e="0"+e;return e},h=function(e){var t=e.y,r=e.m,i=e.d,o=e._,s=e.dayName,a=e.short,c=void 0!==a&&a,u=new Date,l=new Date;l.setDate(l[o+"Date"]()-1);var f=new Date;return(f.setDate(f[o+"Date"]()+1),u[o+"FullYear"]()===t&&u[o+"Month"]()===r&&u[o+"Date"]()===i)?c?"Tdy":"Today":l[o+"FullYear"]()===t&&l[o+"Month"]()===r&&l[o+"Date"]()===i?c?"Ysd":"Yesterday":f[o+"FullYear"]()===t&&f[o+"Month"]()===r&&f[o+"Date"]()===i?c?"Tmw":"Tomorrow":s},d=function(e){var t=new Date(e.getFullYear(),e.getMonth(),e.getDate());t.setDate(t.getDate()-(t.getDay()+6)%7+3);var r=new Date(t.getFullYear(),0,4);r.setDate(r.getDate()-(r.getDay()+6)%7+3);var i=t.getTimezoneOffset()-r.getTimezoneOffset();return t.setHours(t.getHours()-i),1+Math.floor((t-r)/(864e5*7))},y=function(e){var t=e.getDay();return 0===t&&(t=7),t},g=function(e){return null===e?"null":void 0===e?"undefined":"object"!==o(e)?o(e):Array.isArray(e)?"array":({}).toString.call(e).slice(8,-1).toLowerCase()};void 0===(i=(function(){return f}).call(t,r,t,e))||(e.exports=i)}(void 0)},4114:(e,t,r)=>{"use strict";var i=r(6518),o=r(8981),s=r(6198),a=r(4527),c=r(6837);i({target:"Array",proto:!0,arity:1,forced:r(9039)(function(){return 0x100000001!==[].push.call({length:0x100000000},1)})||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=o(this),r=s(t),i=arguments.length;c(r+i);for(var u=0;u<i;u++)t[r]=arguments[u],r++;return a(t,r),r}})},4117:e=>{"use strict";e.exports=function(e){return null==e}},4160:e=>{"use strict";e.exports={DATE_FORMAT:"yyyy-mm-dd HH:MM:ss.l o",ERROR_LIKE_KEYS:["err","error"],MESSAGE_KEY:"msg",LEVEL_KEY:"level",LEVEL_LABEL:"levelLabel",TIMESTAMP_KEY:"time",LEVELS:{default:"USERLVL",60:"FATAL",50:"ERROR",40:"WARN",30:"INFO",20:"DEBUG",10:"TRACE"},LEVEL_NAMES:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},LOGGER_KEYS:["pid","hostname","name","level","time","timestamp","caller"]}},4190:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(2248),c=r(6223),u=a.Map,l=a.set;i({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=new u;return c(t,function(e,o){r(e,o,t)&&l(i,o,e)}),i}})},4198:(e,t,r)=>{var i=r(2203);"disable"===process.env.READABLE_STREAM&&i?(e.exports=i.Readable,Object.assign(e.exports,i),e.exports.Stream=i):((t=e.exports=r(5412)).Stream=i||t,t.Readable=t,t.Writable=r(6708),t.Duplex=r(5382),t.Transform=r(4610),t.PassThrough=r(3600),t.finished=r(6238),t.pipeline=r(7758))},4204:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402).add,s=r(9286),a=r(3789),c=r(507);e.exports=function(e){var t=i(this),r=a(e).getIterator(),u=s(t);return c(r,function(e){o(u,e)}),u}},4209:(e,t,r)=>{"use strict";var i=r(8227),o=r(6269),s=i("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[s]===e)}},4215:(e,t,r)=>{"use strict";var i=r(4576),o=r(2839),s=r(2195),a=function(e){return o.slice(0,e.length)===e};e.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"},4216:e=>{"use strict";e.exports={nullTime:()=>"",epochTime:()=>`,"time":${Date.now()}`,unixTime:()=>`,"time":${Math.round(Date.now()/1e3)}`,isoTime:()=>`,"time":"${new Date(Date.now()).toISOString()}"`}},4245:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e){super("The response of yt-dlp is malformed."),this.name="YtDlpInvalidResponse",this.response=e}}},4270:(e,t,r)=>{"use strict";var i=r(9565),o=r(4901),s=r(34),a=TypeError;e.exports=function(e,t){var r,c;if("string"===t&&o(r=e.toString)&&!s(c=i(r,e))||o(r=e.valueOf)&&!s(c=i(r,e))||"string"!==t&&o(r=e.toString)&&!s(c=i(r,e)))return c;throw new a("Can't convert object to primitive value")}},4376:(e,t,r)=>{"use strict";var i=r(2195);e.exports=Array.isArray||function(e){return"Array"===i(e)}},4377:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(7080),a=r(8469);i({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=a(t,function(e){if(r(e,e,t))return{value:e}},!0);return i&&i.value}})},4402:(e,t,r)=>{"use strict";var i=r(9504),o=Set.prototype;e.exports={Set:Set,add:i(o.add),has:i(o.has),remove:i(o.delete),proto:o}},4428:(e,t,r)=>{"use strict";var i=r(8227)("iterator"),o=!1;try{var s=0,a={next:function(){return{done:!!s++}},return:function(){o=!0}};a[i]=function(){return this},Array.from(a,function(){throw 2})}catch(e){}e.exports=function(e,t){try{if(!t&&!o)return!1}catch(e){return!1}var r=!1;try{var s={};s[i]=function(){return{next:function(){return{done:r=!0}}}},e(s)}catch(e){}return r}},4434:e=>{"use strict";e.exports=require("events")},4449:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402).has,s=r(5170),a=r(3789),c=r(8469),u=r(507),l=r(9539);e.exports=function(e){var t=i(this),r=a(e);if(s(t)<=r.size)return!1!==c(t,function(e){if(r.includes(e))return!1},!0);var f=r.getIterator();return!1!==u(f,function(e){if(o(t,e))return l(f,"normal",!1)})}},4470:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e){super(`The audio data is incomplete: ${e}`),this.name="IncompleteAudioData"}}},4483:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(3650);i({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){return o(a,this,s(e))}})},4495:(e,t,r)=>{"use strict";var i=r(9519),o=r(9039),s=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!o(function(){var e=Symbol("symbol detection");return!s(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&i&&i<41})},4520:(e,t,r)=>{"use strict";r(2489)},4527:(e,t,r)=>{"use strict";var i=r(3724),o=r(4376),s=TypeError,a=Object.getOwnPropertyDescriptor;e.exports=i&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}()?function(e,t){if(o(e)&&!a(e,"length").writable)throw new s("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4538:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(7333),r(4520),r(8992),r(8159),r(7550);let i=r(9896),o=r(9278),s=r(6928),a=r(7016).parse,{logScope:c}=r(5469),u=c("server"),l=r(6145),f=r(8114),p=r(9664),{isHost:h}=r(5853),d={core:{mitm:(e,t)=>{if("/proxy.pac"===e.url){let r=a("http://"+e.headers.host);t.writeHead(200,{"Content-Type":"application/x-ns-proxy-autoconfig"}),t.end(`
					function FindProxyForURL(url, host) {
						if (${Array.from(f.target.host).map(e=>`host == '${e}'`).join(" || ")}) {
							return 'PROXY ${r.hostname}:${r.port||80}'
						}
						return 'DIRECT'
					}
				`)}else{let r={res:t,req:e};Promise.resolve().then(()=>d.protect(r)).then(()=>d.authenticate(r)).then(()=>f.request.before(r)).then(()=>d.filter(r)).then(()=>d.log(r)).then(()=>d.mitm.request(r)).then(()=>f.request.after(r)).then(()=>d.mitm.response(r)).catch(()=>d.mitm.close(r))}},tunnel:(e,t,r)=>{let i={req:e,socket:t,head:r};Promise.resolve().then(()=>d.protect(i)).then(()=>d.authenticate(i)).then(()=>f.connect.before(i)).then(()=>d.filter(i)).then(()=>d.log(i)).then(()=>d.tunnel.connect(i)).then(()=>d.tunnel.dock(i)).then(()=>f.negotiate.before(i)).then(()=>d.tunnel.pipe(i)).catch(()=>d.tunnel.close(i))}},abort:e=>{e&&e.end(),e&&!e.destroyed&&e.destroy()},protect:e=>{let{req:t,res:r,socket:i}=e;t&&t.on("error",()=>d.abort(t.socket,"req")),r&&r.on("error",()=>d.abort(r.socket,"res")),i&&i.on("error",()=>d.abort(i,"socket"))},log:e=>{let{req:t,socket:r,decision:i}=e;r&&(r?u.debug({decision:i,url:t.url},"TUNNEL"):u.debug({decision:i,host:a(t.url).host,encrypted:t.socket.encrypted},`MITM${t.socket.encrypted?" (ssl)":""}`))},authenticate:e=>{let{req:t,res:r,socket:i}=e,o=Buffer.from((t.headers["proxy-authorization"]||"").split(/\s+/).pop()||"","base64").toString();if("proxy-authorization"in t.headers&&delete t.headers["proxy-authorization"],b.authentication&&o!==b.authentication&&(i||t.url.startsWith("http://")))return i?i.write(`HTTP/1.1 407 Proxy Auth Required\r
Proxy-Authenticate: Basic realm="realm"\r
\r
`):r.writeHead(407,{"proxy-authenticate":'Basic realm="realm"'}),Promise.reject(e.error="authenticate")},filter:e=>{if(e.decision||e.req.local)return;let t=a((e.socket?"https://":"")+e.req.url),r=e=>-1!==t.href.search(RegExp(e,"g"));try{let t=b.whitelist.some(r),i=b.blacklist.some(r);if(!t&&i)return Promise.reject(e.error="filter")}catch(t){e.error=t}},mitm:{request:e=>new Promise((t,r)=>{if("close"===e.decision)return r(e.error=e.decision);let{req:i}=e;h(i.url,"bilivideo.com")&&(i.headers.referer="https://www.bilibili.com/",i.headers["user-agent"]="okhttp/3.4.1");let o=a(i.url),s=p.configure(i.method,o,i.headers);e.proxyReq=p.create(o)(s).on("response",r=>t(e.proxyRes=r)).on("error",t=>r(e.error=t)),i.readable?i.pipe(e.proxyReq):e.proxyReq.end(i.body)}),response:e=>{let{res:t,proxyRes:r}=e;r.on("error",()=>d.abort(r.socket,"proxyRes")),t.writeHead(r.statusCode,r.headers),r.readable?r.pipe(t):t.end(r.body)},close:e=>{d.abort(e.res.socket,"mitm")}},tunnel:{connect:e=>new Promise((t,r)=>{if("close"===e.decision)return r(e.error=e.decision);let{req:i}=e,s=a("https://"+i.url);if(global.proxy&&!i.local){let o=p.configure(i.method,s,i.headers);p.create(d)(o).on("connect",(r,i)=>t(e.proxySocket=i)).on("error",t=>r(e.error=t)).end()}else{let i=o.connect(s.port||443,p.translate(s.hostname)).on("connect",()=>t(e.proxySocket=i)).on("error",t=>r(e.error=t))}}),dock:e=>new Promise(t=>{let{req:r,head:i,socket:o}=e;o.once("data",r=>t(e.head=Buffer.concat([i,r]))).write(`HTTP/${r.httpVersion} 200 Connection established\r
\r
`)}).then(t=>e.socket.sni=l(t)).catch(e=>e&&u.error(e)),pipe:e=>{if("blank"===e.decision)return Promise.reject(e.error=e.decision);let{head:t,socket:r,proxySocket:i}=e;i.on("error",()=>d.abort(e.proxySocket,"proxySocket")),i.write(t),r.pipe(i),i.pipe(r)},close:e=>{d.abort(e.socket,"tunnel")}}},y=process.env.SIGN_CERT||s.join(__dirname,"..","server.crt"),g=process.env.SIGN_KEY||s.join(__dirname,"..","server.key"),m={key:i.readFileSync(g),cert:i.readFileSync(y)},b={http:r(8611).createServer().on("request",d.core.mitm).on("connect",d.core.tunnel),https:r(5692).createServer(m).on("request",d.core.mitm).on("connect",d.core.tunnel)};b.whitelist=[],b.blacklist=["://127\\.\\d+\\.\\d+\\.\\d+","://localhost"],b.authentication=null,e.exports=b},4549:(e,t,r)=>{"use strict";var i=r(4576);e.exports=function(e,t){var r=i.Iterator,o=r&&r.prototype,s=o&&o[e],a=!1;if(s)try{s.call({next:function(){return{done:!0}},return:function(){a=!0}},-1)}catch(e){e instanceof t||(a=!1)}if(!a)return s}},4576:function(e){"use strict";var t=function(e){return e&&e.Math===Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof global&&global)||t("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4601:(e,t,r)=>{"use strict";var i=r(7751),o=r(9297),s=r(6699),a=r(1625),c=r(2967),u=r(7740),l=r(1056),f=r(3167),p=r(2603),h=r(7584),d=r(747),y=r(3724),g=r(6395);e.exports=function(e,t,r,m){var b="stackTraceLimit",v=m?2:1,w=e.split("."),_=w[w.length-1],S=i.apply(null,w);if(S){var x=S.prototype;if(!g&&o(x,"cause")&&delete x.cause,!r)return S;var E=i("Error"),O=t(function(e,t){var r=p(m?t:e,void 0),i=m?new S(e):new S;return void 0!==r&&s(i,"message",r),d(i,O,i.stack,2),this&&a(x,this)&&f(i,this,O),arguments.length>v&&h(i,arguments[v]),i});if(O.prototype=x,"Error"!==_?c?c(O,E):u(O,E,{name:!0}):y&&b in S&&(l(O,S,b),l(O,S,"prepareStackTrace")),u(O,S),!g)try{x.name!==_&&s(x,"name",_),x.constructor=O}catch(e){}return O}}},4610:(e,t,r)=>{"use strict";e.exports=f;var i=r(113).F,o=i.ERR_METHOD_NOT_IMPLEMENTED,s=i.ERR_MULTIPLE_CALLBACK,a=i.ERR_TRANSFORM_ALREADY_TRANSFORMING,c=i.ERR_TRANSFORM_WITH_LENGTH_0,u=r(5382);function l(e,t){var r=this._transformState;r.transforming=!1;var i=r.writecb;if(null===i)return this.emit("error",new s);r.writechunk=null,r.writecb=null,null!=t&&this.push(t),i(e);var o=this._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&this._read(o.highWaterMark)}function f(e){if(!(this instanceof f))return new f(e);u.call(this,e),this._transformState={afterTransform:l.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",p)}function p(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?h(this,null,null):this._flush(function(t,r){h(e,t,r)})}function h(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new c;if(e._transformState.transforming)throw new a;return e.push(null)}r(2017)(f,u),f.prototype.push=function(e,t){return this._transformState.needTransform=!1,u.prototype.push.call(this,e,t)},f.prototype._transform=function(e,t,r){r(new o("_transform()"))},f.prototype._write=function(e,t,r){var i=this._transformState;if(i.writecb=r,i.writechunk=e,i.writeencoding=t,!i.transforming){var o=this._readableState;(i.needTransform||o.needReadable||o.length<o.highWaterMark)&&this._read(o.highWaterMark)}},f.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},f.prototype._destroy=function(e,t){u.prototype._destroy.call(this,e,function(e){t(e)})}},4615:(e,t,r)=>{"use strict";let{Transform:i}=r(2203),{StringDecoder:o}=r(3193),s=Symbol("last"),a=Symbol("decoder");function c(e,t,r){let i;if(this.overflow){if(1===(i=this[a].write(e).split(this.matcher)).length)return r();i.shift(),this.overflow=!1}else this[s]+=this[a].write(e),i=this[s].split(this.matcher);this[s]=i.pop();for(let e=0;e<i.length;e++)try{l(this,this.mapper(i[e]))}catch(e){return r(e)}if(this.overflow=this[s].length>this.maxLength,this.overflow&&!this.skipOverflow)return void r(Error("maximum buffer reached"));r()}function u(e){if(this[s]+=this[a].end(),this[s])try{l(this,this.mapper(this[s]))}catch(t){return e(t)}e()}function l(e,t){void 0!==t&&e.push(t)}function f(e){return e}e.exports=function(e,t,r){switch(e=e||/\r?\n/,t=t||f,r=r||{},arguments.length){case 1:"function"==typeof e?(t=e,e=/\r?\n/):"object"!=typeof e||e instanceof RegExp||e[Symbol.split]||(r=e,e=/\r?\n/);break;case 2:"function"==typeof e?(r=t,t=e,e=/\r?\n/):"object"==typeof t&&(r=t,t=f)}(r=Object.assign({},r)).autoDestroy=!0,r.transform=c,r.flush=u,r.readableObjectMode=!0;let l=new i(r);return l[s]="",l[a]=new o("utf8"),l.matcher=e,l.mapper=t,l.maxLength=r.maxLength,l.skipOverflow=r.skipOverflow||!1,l.overflow=!1,l._destroy=function(e,t){this._writableState.errorEmitted=!1,t(e)},l}},4659:(e,t,r)=>{"use strict";var i=r(3724),o=r(4913),s=r(6980);e.exports=function(e,t,r){i?o.f(e,t,s(0,r)):e[t]=r}},4901:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4905:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(8551),c=r(34),u=r(7751),l=r(1767),f=r(772),p=u("Promise"),h=TypeError;i({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{reduce:function(e){a(this),s(e);var t=l(this),r=t.iterator,i=t.next,u=arguments.length<2,d=u?void 0:arguments[1],y=0;return new p(function(t,s){var l=function(e){f(r,s,e,s)},g=function(){try{p.resolve(a(o(i,r))).then(function(r){try{if(a(r).done)u?s(new h("Reduce of empty iterator with no initial value")):t(d);else{var i=r.value;if(u)u=!1,d=i,g();else try{var o=e(d,i,y),f=function(e){d=e,g()};c(o)?p.resolve(o).then(f,l):f(o)}catch(e){l(e)}}y++}catch(e){s(e)}},s)}catch(e){s(e)}};g()})}})},4913:(e,t,r)=>{"use strict";var i=r(3724),o=r(5917),s=r(8686),a=r(8551),c=r(6969),u=TypeError,l=Object.defineProperty,f=Object.getOwnPropertyDescriptor,p="enumerable",h="configurable",d="writable";t.f=i?s?function(e,t,r){if(a(e),t=c(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var i=f(e,t);i&&i[d]&&(e[t]=r.value,r={configurable:h in r?r[h]:i[h],enumerable:p in r?r[p]:i[p],writable:!1})}return l(e,t,r)}:l:function(e,t,r){if(a(e),t=c(t),a(r),o)try{return l(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},5026:e=>{"use strict";e.exports=function e(i){if(!(i instanceof Error))return i;i[r]=void 0;let s=Object.create(o);for(let o in s.type="[object Function]"===t.call(i.constructor)?i.constructor.name:i.name,s.message=i.message,s.stack=i.stack,i)if(void 0===s[o]){let t=i[o];t instanceof Error?t.hasOwnProperty(r)||(s[o]=e(t)):s[o]=t}return delete i[r],s.raw=i,s};let{toString:t}=Object.prototype,r=Symbol("circular-ref-tag"),i=Symbol("pino-raw-err-ref"),o=Object.create({},{type:{enumerable:!0,writable:!0,value:void 0},message:{enumerable:!0,writable:!0,value:void 0},stack:{enumerable:!0,writable:!0,value:void 0},raw:{enumerable:!1,get:function(){return this[i]},set:function(e){this[i]=e}}});Object.defineProperty(o,i,{writable:!0,value:{}})},5031:(e,t,r)=>{"use strict";var i=r(7751),o=r(9504),s=r(8480),a=r(3717),c=r(8551),u=o([].concat);e.exports=i("Reflect","ownKeys")||function(e){var t=s.f(c(e)),r=a.f;return r?u(t,r(e)):t}},5122:(e,t,r)=>{"use strict";var i=r(6518),o=r(6194),s=r(2652),a=r(2248).set;i({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=o(this),r=arguments.length,i=0;i<r;)s(arguments[i++],function(e,r){a(t,e,r)},{AS_ENTRIES:!0});return t}})},5146:(e,t,r)=>{"use strict";let i=r(9896),o=r(4434),s=r(9023).inherits,a=r(6928),c=r(7814);function u(e,t){function r(r,i){if(r){t._reopening=!1,t._writing=!1,t._opening=!1,t.sync?process.nextTick(()=>{t.listenerCount("error")>0&&t.emit("error",r)}):t.emit("error",r);return}t.fd=i,t.file=e,t._reopening=!1,t._opening=!1,t._writing=!1,t.sync?process.nextTick(()=>t.emit("ready")):t.emit("ready"),!t._reopening&&(t._writing||!(t._len>t.minLength)||t.destroyed||p(t))}t._opening=!0,t._writing=!0,t._asyncDrainScheduled=!1;let o=t.append?"a":"w",s=t.mode;if(t.sync)try{t.mkdir&&i.mkdirSync(a.dirname(e),{recursive:!0});let c=i.openSync(e,o,s);r(null,c)}catch(e){throw r(e),e}else t.mkdir?i.mkdir(a.dirname(e),{recursive:!0},t=>{if(t)return r(t);i.open(e,o,s,r)}):i.open(e,o,s,r)}function l(e){if(!(this instanceof l))return new l(e);let{fd:t,dest:r,minLength:o,maxLength:s,maxWrite:a,sync:d,append:y=!0,mode:g,mkdir:m,retryEAGAIN:b}=e||{};if(t=t||r,this._bufs=[],this._len=0,this.fd=-1,this._writing=!1,this._writingBuf="",this._ending=!1,this._reopening=!1,this._asyncDrainScheduled=!1,this._hwm=Math.max(o||0,16387),this.file=null,this.destroyed=!1,this.minLength=o||0,this.maxLength=s||0,this.maxWrite=a||16384,this.sync=d||!1,this.append=y||!1,this.mode=g,this.retryEAGAIN=b||(()=>!0),this.mkdir=m||!1,"number"==typeof t)this.fd=t,process.nextTick(()=>this.emit("ready"));else if("string"==typeof t)u(t,this);else throw Error("SonicBoom supports only file descriptors and files");if(this.minLength>=this.maxWrite)throw Error(`minLength should be smaller than maxWrite (${this.maxWrite})`);this.release=(e,t)=>{if(e){if("EAGAIN"===e.code&&this.retryEAGAIN(e,this._writingBuf.length,this._len-this._writingBuf.length))if(this.sync)try{c(100),this.release(void 0,0)}catch(e){this.release(e)}else setTimeout(()=>{i.write(this.fd,this._writingBuf,"utf8",this.release)},100);else this._writing=!1,this.emit("error",e);return}if(this.emit("write",t),this._len-=t,this._writingBuf=this._writingBuf.slice(t),this._writingBuf.length){if(!this.sync)return void i.write(this.fd,this._writingBuf,"utf8",this.release);try{do{let e=i.writeSync(this.fd,this._writingBuf,"utf8");this._len-=e,this._writingBuf=this._writingBuf.slice(e)}while(this._writingBuf)}catch(e){this.release(e);return}}let r=this._len;this._reopening?(this._writing=!1,this._reopening=!1,this.reopen()):r>this.minLength?p(this):this._ending?r>0?p(this):(this._writing=!1,h(this)):(this._writing=!1,this.sync?this._asyncDrainScheduled||(this._asyncDrainScheduled=!0,process.nextTick(f,this)):this.emit("drain"))},this.on("newListener",function(e){"drain"===e&&(this._asyncDrainScheduled=!1)})}function f(e){e.listenerCount("drain")>0&&(e._asyncDrainScheduled=!1,e.emit("drain"))}function p(e){let t=e.release;if(e._writing=!0,e._writingBuf=e._writingBuf||e._bufs.shift()||"",e.sync)try{let r=i.writeSync(e.fd,e._writingBuf,"utf8");t(null,r)}catch(e){t(e)}else i.write(e.fd,e._writingBuf,"utf8",t)}function h(e){if(-1===e.fd)return void e.once("ready",h.bind(null,e));function t(t){if(t)return void e.emit("error",t);e._ending&&!e._writing&&e.emit("finish"),e.emit("close")}e.destroyed=!0,e._bufs=[],1!==e.fd&&2!==e.fd?i.close(e.fd,t):setImmediate(t)}s(l,o),l.prototype.write=function(e){if(this.destroyed)throw Error("SonicBoom destroyed");let t=this._len+e.length,r=this._bufs;return this.maxLength&&t>this.maxLength?this.emit("drop",e):(0===r.length||r[r.length-1].length+e.length>this.maxWrite?r.push(""+e):r[r.length-1]+=e,this._len=t,!this._writing&&this._len>=this.minLength&&p(this)),this._len<this._hwm},l.prototype.flush=function(){if(this.destroyed)throw Error("SonicBoom destroyed");this._writing||this.minLength<=0||(0===this._bufs.length&&this._bufs.push(""),p(this))},l.prototype.reopen=function(e){if(this.destroyed)throw Error("SonicBoom destroyed");if(this._opening)return void this.once("ready",()=>{this.reopen(e)});if(this._ending)return;if(!this.file)throw Error("Unable to reopen a file descriptor, you must pass a file to SonicBoom");if(this._reopening=!0,this._writing)return;let t=this.fd;this.once("ready",()=>{t!==this.fd&&i.close(t,e=>{if(e)return this.emit("error",e)})}),u(e||this.file,this)},l.prototype.end=function(){if(this.destroyed)throw Error("SonicBoom destroyed");if(this._opening)return void this.once("ready",()=>{this.end()});!this._ending&&(this._ending=!0,this._writing||(this._len>0&&this.fd>=0?p(this):h(this)))},l.prototype.flushSync=function(){if(this.destroyed)throw Error("SonicBoom destroyed");if(this.fd<0)throw Error("sonic boom is not ready yet");for(!this._writing&&this._writingBuf.length>0&&(this._bufs.unshift(this._writingBuf),this._writingBuf="");this._bufs.length;){let e=this._bufs[0];try{this._len-=i.writeSync(this.fd,e,"utf8"),this._bufs.shift()}catch(t){if("EAGAIN"!==t.code||!this.retryEAGAIN(t,e.length,this._len-e.length))throw t;c(100)}}},l.prototype.destroy=function(){this.destroyed||h(this)},l.SonicBoom=l,l.default=l,e.exports=l},5170:(e,t,r)=>{"use strict";e.exports=r(6706)(r(4402).proto,"size","get")||function(e){return e.size}},5213:(e,t,r)=>{"use strict";var i=r(4576),o=r(9039),s=i.RegExp;e.exports={correct:!o(function(){var e=!0;try{s(".","d")}catch(t){e=!1}var t={},r="",i=e?"dgimsy":"gimsy",o=function(e,i){Object.defineProperty(t,e,{get:function(){return r+=i,!0}})},a={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var c in e&&(a.hasIndices="d"),a)o(c,a[c]);return Object.getOwnPropertyDescriptor(s.prototype,"flags").get.call(t)!==i||r!==i})}},5223:(e,t,r)=>{"use strict";var i=r(6518),o=r(7080),s=r(4402).remove;i({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=o(this),r=!0,i=0,a=arguments.length;i<a;i++)e=s(t,arguments[i]),r=r&&e;return!!r}})},5291:(e,t,r)=>{"use strict";var i=r(113).F.ERR_INVALID_OPT_VALUE;e.exports={getHighWaterMark:function(e,t,r,o){var s=null!=t.highWaterMark?t.highWaterMark:o?t[r]:null;if(null!=s){if(!(isFinite(s)&&Math.floor(s)===s)||s<0)throw new i(o?r:"highWaterMark",s);return Math.floor(s)}return e.objectMode?16:16384}}},5317:e=>{"use strict";e.exports=require("child_process")},5332:e=>{e.exports=function(e){var t=e._readableState;return t?t.objectMode||"number"==typeof e._duplexState?e.read():e.read(function(e){if(e.buffer.length){var t=e.bufferIndex||0;if(e.buffer.head)return e.buffer.head.data.length;if(e.buffer.length-t>0&&e.buffer[t])return e.buffer[t].length}return e.length}(t)):null}},5382:(e,t,r)=>{"use strict";var i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=l;var o=r(5412),s=r(6708);r(2017)(l,o);for(var a=i(s.prototype),c=0;c<a.length;c++){var u=a[c];l.prototype[u]||(l.prototype[u]=s.prototype[u])}function l(e){if(!(this instanceof l))return new l(e);o.call(this,e),s.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",f)))}function f(){this._writableState.ended||process.nextTick(p,this)}function p(e){e.end()}Object.defineProperty(l.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(l.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(l.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(l.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}})},5397:(e,t,r)=>{"use strict";var i=r(7055),o=r(7750);e.exports=function(e){return i(o(e))}},5412:(e,t,r)=>{"use strict";e.exports=O,O.ReadableState=E,r(4434).EventEmitter;var i,o,s,a,c,u=function(e,t){return e.listeners(t).length},l=r(1416),f=r(181).Buffer,p=("undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},h=r(9023);o=h&&h.debuglog?h.debuglog("stream"):function(){};var d=r(345),y=r(5896),g=r(5291).getHighWaterMark,m=r(113).F,b=m.ERR_INVALID_ARG_TYPE,v=m.ERR_STREAM_PUSH_AFTER_EOF,w=m.ERR_METHOD_NOT_IMPLEMENTED,_=m.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r(2017)(O,l);var S=y.errorOrDestroy,x=["error","close","destroy","pause","resume"];function E(e,t,o){i=i||r(5382),e=e||{},"boolean"!=typeof o&&(o=t instanceof i),this.objectMode=!!e.objectMode,o&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=g(this,e,"readableHighWaterMark",o),this.buffer=new d,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==e.emitClose,this.autoDestroy=!!e.autoDestroy,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(s||(s=r(3141).I),this.decoder=new s(e.encoding),this.encoding=e.encoding)}function O(e){if(i=i||r(5382),!(this instanceof O))return new O(e);var t=this instanceof i;this._readableState=new E(e,this,t),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),l.call(this)}function k(e,t,r,i,s){o("readableAddChunk",t);var a,c,u=e._readableState;if(null===t)u.reading=!1,function(e,t){if(o("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?R(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,P(e)))}}(e,u);else if(s||(c=function(e,t){var r;return f.isBuffer(t)||t instanceof p||"string"==typeof t||void 0===t||e.objectMode||(r=new b("chunk",["string","Buffer","Uint8Array"],t)),r}(u,t)),c)S(e,c);else if(u.objectMode||t&&t.length>0)if("string"==typeof t||u.objectMode||Object.getPrototypeOf(t)===f.prototype||(a=t,t=f.from(a)),i)u.endEmitted?S(e,new _):j(e,u,t,!0);else if(u.ended)S(e,new v);else{if(u.destroyed)return!1;u.reading=!1,u.decoder&&!r?(t=u.decoder.write(t),u.objectMode||0!==t.length?j(e,u,t,!1):L(e,u)):j(e,u,t,!1)}else i||(u.reading=!1,L(e,u));return!u.ended&&(u.length<u.highWaterMark||0===u.length)}function j(e,t,r,i){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&R(e)),L(e,t)}function T(e,t){var r;if(e<=0||0===t.length&&t.ended)return 0;if(t.objectMode)return 1;if(e!=e)if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length;return(e>t.highWaterMark&&((r=e)>=0x40000000?r=0x40000000:(r--,r|=r>>>1,r|=r>>>2,r|=r>>>4,r|=r>>>8,r|=r>>>16,r++),t.highWaterMark=r),e<=t.length)?e:t.ended?t.length:(t.needReadable=!0,0)}function R(e){var t=e._readableState;o("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(o("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(P,e))}function P(e){var t=e._readableState;o("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,I(e)}function L(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(M,e,t))}function M(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var r=t.length;if(o("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}function A(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function N(e){o("readable nexttick read 0"),e.read(0)}function C(e,t){o("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),I(e),t.flowing&&!t.reading&&e.read(0)}function I(e){var t=e._readableState;for(o("flow",t.flowing);t.flowing&&null!==e.read(););}function D(e,t){var r;return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r)}function B(e){var t=e._readableState;o("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick($,t,e))}function $(e,t){if(o("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}function q(e,t){for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r;return -1}Object.defineProperty(O.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),O.prototype.destroy=y.destroy,O.prototype._undestroy=y.undestroy,O.prototype._destroy=function(e,t){t(e)},O.prototype.push=function(e,t){var r,i=this._readableState;return i.objectMode?r=!0:"string"==typeof e&&((t=t||i.defaultEncoding)!==i.encoding&&(e=f.from(e,t),t=""),r=!0),k(this,e,t,!1,r)},O.prototype.unshift=function(e){return k(this,e,null,!0,!1)},O.prototype.isPaused=function(){return!1===this._readableState.flowing},O.prototype.setEncoding=function(e){s||(s=r(3141).I);var t=new s(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var i=this._readableState.buffer.head,o="";null!==i;)o+=t.write(i.data),i=i.next;return this._readableState.buffer.clear(),""!==o&&this._readableState.buffer.push(o),this._readableState.length=o.length,this},O.prototype.read=function(e){o("read",e),e=parseInt(e,10);var t,r=this._readableState,i=e;if(0!==e&&(r.emittedReadable=!1),0===e&&r.needReadable&&((0!==r.highWaterMark?r.length>=r.highWaterMark:r.length>0)||r.ended))return o("read: emitReadable",r.length,r.ended),0===r.length&&r.ended?B(this):R(this),null;if(0===(e=T(e,r))&&r.ended)return 0===r.length&&B(this),null;var s=r.needReadable;return o("need readable",s),(0===r.length||r.length-e<r.highWaterMark)&&o("length less than watermark",s=!0),r.ended||r.reading?o("reading or ended",s=!1):s&&(o("do read"),r.reading=!0,r.sync=!0,0===r.length&&(r.needReadable=!0),this._read(r.highWaterMark),r.sync=!1,r.reading||(e=T(i,r))),null===(t=e>0?D(e,r):null)?(r.needReadable=r.length<=r.highWaterMark,e=0):(r.length-=e,r.awaitDrain=0),0===r.length&&(r.ended||(r.needReadable=!0),i!==e&&r.ended&&B(this)),null!==t&&this.emit("data",t),t},O.prototype._read=function(e){S(this,new w("_read()"))},O.prototype.pipe=function(e,t){var r,i=this,s=this._readableState;switch(s.pipesCount){case 0:s.pipes=e;break;case 1:s.pipes=[s.pipes,e];break;default:s.pipes.push(e)}s.pipesCount+=1,o("pipe count=%d opts=%j",s.pipesCount,t);var a=t&&!1===t.end||e===process.stdout||e===process.stderr?g:c;function c(){o("onend"),e.end()}s.endEmitted?process.nextTick(a):i.once("end",a),e.on("unpipe",function t(r,a){o("onunpipe"),r===i&&a&&!1===a.hasUnpiped&&(a.hasUnpiped=!0,o("cleanup"),e.removeListener("close",d),e.removeListener("finish",y),e.removeListener("drain",l),e.removeListener("error",h),e.removeListener("unpipe",t),i.removeListener("end",c),i.removeListener("end",g),i.removeListener("data",p),f=!0,s.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&l())});var l=(r=i,function(){var e=r._readableState;o("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&u(r,"data")&&(e.flowing=!0,I(r))});e.on("drain",l);var f=!1;function p(t){o("ondata");var r=e.write(t);o("dest.write",r),!1===r&&((1===s.pipesCount&&s.pipes===e||s.pipesCount>1&&-1!==q(s.pipes,e))&&!f&&(o("false write response, pause",s.awaitDrain),s.awaitDrain++),i.pause())}function h(t){o("onerror",t),g(),e.removeListener("error",h),0===u(e,"error")&&S(e,t)}function d(){e.removeListener("finish",y),g()}function y(){o("onfinish"),e.removeListener("close",d),g()}function g(){o("unpipe"),i.unpipe(e)}return i.on("data",p),!function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",h),e.once("close",d),e.once("finish",y),e.emit("pipe",i),s.flowing||(o("pipe resume"),i.resume()),e},O.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var i=t.pipes,o=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var s=0;s<o;s++)i[s].emit("unpipe",this,{hasUnpiped:!1});return this}var a=q(t.pipes,e);return -1===a||(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},O.prototype.on=function(e,t){var r=l.prototype.on.call(this,e,t),i=this._readableState;return"data"===e?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"!==e||i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,o("on readable",i.length,i.reading),i.length?R(this):i.reading||process.nextTick(N,this)),r},O.prototype.addListener=O.prototype.on,O.prototype.removeListener=function(e,t){var r=l.prototype.removeListener.call(this,e,t);return"readable"===e&&process.nextTick(A,this),r},O.prototype.removeAllListeners=function(e){var t=l.prototype.removeAllListeners.apply(this,arguments);return("readable"===e||void 0===e)&&process.nextTick(A,this),t},O.prototype.resume=function(){var e,t,r=this._readableState;return r.flowing||(o("resume"),r.flowing=!r.readableListening,e=this,(t=r).resumeScheduled||(t.resumeScheduled=!0,process.nextTick(C,e,t))),r.paused=!1,this},O.prototype.pause=function(){return o("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(o("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},O.prototype.wrap=function(e){var t=this,r=this._readableState,i=!1;for(var s in e.on("end",function(){if(o("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(s){if(o("wrapped data"),r.decoder&&(s=r.decoder.write(s)),!r.objectMode||null!=s)(r.objectMode||s&&s.length)&&(t.push(s)||(i=!0,e.pause()))}),e)void 0===this[s]&&"function"==typeof e[s]&&(this[s]=function(t){return function(){return e[t].apply(e,arguments)}}(s));for(var a=0;a<x.length;a++)e.on(x[a],this.emit.bind(this,x[a]));return this._read=function(t){o("wrapped _read",t),i&&(i=!1,e.resume())},this},"function"==typeof Symbol&&(O.prototype[Symbol.asyncIterator]=function(){return void 0===a&&(a=r(2955)),a(this)}),Object.defineProperty(O.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(O.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(O.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),O._fromList=D,Object.defineProperty(O.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(O.from=function(e,t){return void 0===c&&(c=r(6532)),c(O,e,t)})},5469:(e,t,r)=>{"use strict";var i;let o=r(3125),s=process.env.LOG_FILE,a=process.stdout.isTTY,c=o({level:null!=(i=process.env.LOG_LEVEL)?i:"info",prettyPrint:"true"!==process.env.JSON_LOG&&{colorize:a,messageFormat:a?"\x1b[1m\x1b[32m({scope})\x1b[0m\x1b[36m {msg}":"({scope}) {msg}",ignore:"time,pid,hostname,scope",errorProps:"*"}},s&&o.destination(s));e.exports={logger:c,logScope:function(e){return c.child({scope:e})}}},5509:(e,t,r)=>{"use strict";var i=r(6518),o=r(7080),s=r(4402).add;i({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function(){for(var e=o(this),t=0,r=arguments.length;t<r;t++)s(e,arguments[t]);return e}})},5610:(e,t,r)=>{"use strict";var i=r(1291),o=Math.max,s=Math.min;e.exports=function(e,t){var r=i(e);return r<0?o(r+t,0):s(r,t)}},5660:(e,t,r)=>{"use strict";let{version:i}=r(3550);e.exports={version:i}},5685:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e,t){super(`${e} exited with ${t}, which is not zero.`),this.process=e,this.exitCode=t,this.name="ProcessExitNotSuccessfully"}}},5692:e=>{"use strict";e.exports=require("https")},5694:(e,t,r)=>{"use strict";var i=r(6518),o=r(9504),s=r(7080),a=r(8469),c=r(655),u=o([].join),l=o([].push);i({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=s(this),r=void 0===e?",":c(e),i=[];return a(t,function(e){l(i,e)}),u(i,r)}})},5745:(e,t,r)=>{"use strict";var i=r(7629);e.exports=function(e,t){return i[e]||(i[e]=t||{})}},5835:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454);let{cacheStorage:i,CacheStorageGroup:o,getManagedCacheStorage:s}=r(3971),a=r(2569),c=r(9643),u=r(9664),l=e=>({id:e.id,name:e.title,artists:{id:e.mid,name:e.author}}),f=e=>u("GET","https://www.bilibili.com/audio/music-service-c/web/url?rivilege=2&quality=2&sid="+e).then(e=>e.json()).then(e=>0===e.code?e.data.cdns[0].replace("https","http"):Promise.reject()).catch(()=>a().bilibili.track(e)),p=s("provider/bilibili");e.exports={check:e=>p.cache(e,()=>(e=>u("GET",`https://api.bilibili.com/audio/music-service-c/s?search_type=music&page=1&pagesize=30&keyword=${encodeURIComponent(e.keyword)}`).then(e=>e.json()).then(t=>{let r=c(t.data.result.map(l),e);return r?r.id:Promise.reject()}))(e)).then(f),track:f}},5844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if("default"!==r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}(r(2018));let{env:o={},argv:s=[],platform:a=""}="undefined"==typeof process?{}:process,c="NO_COLOR"in o||s.includes("--no-color"),u="FORCE_COLOR"in o||s.includes("--color"),l="dumb"===o.TERM,f=i&&i.isatty&&i.isatty(1)&&o.TERM&&!l,p="CI"in o&&("GITHUB_ACTIONS"in o||"GITLAB_CI"in o||"CIRCLECI"in o),h=!c&&(u||"win32"===a&&!l||f||p),d=(e,t,r,i,o=t.substring(0,e)+i,s=t.substring(e+r.length),a=s.indexOf(r))=>o+(a<0?s:d(a,s,r,i)),y=(e,t,r)=>((e,t,r=e,i=e.length+1)=>o=>o||""!==o&&void 0!==o?((e,t,r,i,o)=>e<0?r+t+i:r+d(e,t,i,o)+i)((""+o).indexOf(t,i),o,e,t,r):"")(`\x1b[${e}m`,`\x1b[${t}m`,r),g={reset:y(0,0),bold:y(1,22,"\x1b[22m\x1b[1m"),dim:y(2,22,"\x1b[22m\x1b[2m"),italic:y(3,23),underline:y(4,24),inverse:y(7,27),hidden:y(8,28),strikethrough:y(9,29),black:y(30,39),red:y(31,39),green:y(32,39),yellow:y(33,39),blue:y(34,39),magenta:y(35,39),cyan:y(36,39),white:y(37,39),gray:y(90,39),bgBlack:y(40,49),bgRed:y(41,49),bgGreen:y(42,49),bgYellow:y(43,49),bgBlue:y(44,49),bgMagenta:y(45,49),bgCyan:y(46,49),bgWhite:y(47,49),blackBright:y(90,39),redBright:y(91,39),greenBright:y(92,39),yellowBright:y(93,39),blueBright:y(94,39),magentaBright:y(95,39),cyanBright:y(96,39),whiteBright:y(97,39),bgBlackBright:y(100,49),bgRedBright:y(101,49),bgGreenBright:y(102,49),bgYellowBright:y(103,49),bgBlueBright:y(104,49),bgMagentaBright:y(105,49),bgCyanBright:y(106,49),bgWhiteBright:y(107,49)},m=({useColor:e=h}={})=>e?g:Object.keys(g).reduce((e,t)=>({...e,[t]:String}),{}),{reset:b,bold:v,dim:w,italic:_,underline:S,inverse:x,hidden:E,strikethrough:O,black:k,red:j,green:T,yellow:R,blue:P,magenta:L,cyan:M,white:A,gray:N,bgBlack:C,bgRed:I,bgGreen:D,bgYellow:B,bgBlue:$,bgMagenta:q,bgCyan:U,bgWhite:F,blackBright:W,redBright:G,greenBright:H,yellowBright:z,blueBright:J,magentaBright:K,cyanBright:V,whiteBright:Y,bgBlackBright:Q,bgRedBright:X,bgGreenBright:Z,bgYellowBright:ee,bgBlueBright:et,bgMagentaBright:er,bgCyanBright:en,bgWhiteBright:ei}=m();t.bgBlack=C,t.bgBlackBright=Q,t.bgBlue=$,t.bgBlueBright=et,t.bgCyan=U,t.bgCyanBright=en,t.bgGreen=D,t.bgGreenBright=Z,t.bgMagenta=q,t.bgMagentaBright=er,t.bgRed=I,t.bgRedBright=X,t.bgWhite=F,t.bgWhiteBright=ei,t.bgYellow=B,t.bgYellowBright=ee,t.black=k,t.blackBright=W,t.blue=P,t.blueBright=J,t.bold=v,t.createColors=m,t.cyan=M,t.cyanBright=V,t.dim=w,t.gray=N,t.green=T,t.greenBright=H,t.hidden=E,t.inverse=x,t.isColorSupported=h,t.italic=_,t.magenta=L,t.magentaBright=K,t.red=j,t.redBright=G,t.reset=b,t.strikethrough=O,t.underline=S,t.white=A,t.whiteBright=Y,t.yellow=R,t.yellowBright=z},5853:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(4905),r(8872),r(8992);let i=(e,t)=>e.includes(t);e.exports={isHost:i,isHostWrapper:e=>t=>i(e,t),cookieToMap:e=>e.split(";").map(e=>e.trim().split("=")).reduce((e,[t,r])=>({...e,[t]:r}),{}),mapToCookie:e=>Object.entries(e).map(([e,t])=>`${e}=${t}`).join("; ")}},5896:e=>{"use strict";function t(e,t){i(e,t),r(e)}function r(e){(!e._writableState||e._writableState.emitClose)&&(!e._readableState||e._readableState.emitClose)&&e.emit("close")}function i(e,t){e.emit("error",t)}e.exports={destroy:function(e,o){var s=this,a=this._readableState&&this._readableState.destroyed,c=this._writableState&&this._writableState.destroyed;return a||c?o?o(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(i,this,e)):process.nextTick(i,this,e)):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!o&&e?s._writableState?s._writableState.errorEmitted?process.nextTick(r,s):(s._writableState.errorEmitted=!0,process.nextTick(t,s,e)):process.nextTick(t,s,e):o?(process.nextTick(r,s),o(e)):process.nextTick(r,s)})),this},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var r=e._readableState,i=e._writableState;r&&r.autoDestroy||i&&i.autoDestroy?e.destroy(t):e.emit("error",t)}}},5903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(8335);let{getManagedCacheStorage:i}=r(3971),{logScope:o}=r(5469),s=r(2141),a=r(2860),{spawnStdout:c}=r(6694),u=o("provider/youtube-dl");async function l(e){try{let{stdout:t}=await c("youtube-dl",e),r=JSON.parse(t.toString());if("object"==typeof r&&"string"==typeof r.id&&"string"==typeof r.url)return r;throw new s(r)}catch(e){if(e&&"ENOENT"===e.code)throw new a;throw e}}let f=async e=>{let t,{url:r}=await l((t=e.keyword,["-f","140","--dump-json",`ytsearch1:${t}`]));return r},p=i("youtube-dl");e.exports={check:e=>p.cache(e,()=>f(e)).catch(e=>{throw e&&u.error(e),e})}},5917:(e,t,r)=>{"use strict";var i=r(3724),o=r(9039),s=r(4055);e.exports=!i&&!o(function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a})},5966:(e,t,r)=>{"use strict";var i=r(9306),o=r(4117);e.exports=function(e,t){var r=e[t];return o(r)?void 0:i(r)}},5971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(8335);let{getManagedCacheStorage:i}=r(3971),{logScope:o}=r(5469),{spawnStdout:s}=r(6694),a=r(4245),c=r(6218),u=o("provider/yt-dlp");async function l(e){try{let{stdout:t}=await s("yt-dlp",e),r=JSON.parse(t.toString());if("object"==typeof r&&"string"==typeof r.id&&"string"==typeof r.url)return r;throw new a(r)}catch(e){if(e&&"ENOENT"===e.code)throw new c;throw e}}let f=async e=>{let t,{url:r}=await l((t=e.keyword,["-f","140","--dump-json",`ytsearch1:${t}`]));return r},p=i("yt-dlp");e.exports={check:e=>p.cache(e,()=>f(e)).catch(e=>{throw e&&u.error(e),e})}},6043:(e,t,r)=>{"use strict";var i=r(9306),o=TypeError,s=function(e){var t,r;this.promise=new e(function(e,i){if(void 0!==t||void 0!==r)throw new o("Bad Promise constructor");t=e,r=i}),this.resolve=i(t),this.reject=i(r)};e.exports.f=function(e){return new s(e)}},6080:(e,t,r)=>{"use strict";var i=r(7476),o=r(9306),s=r(616),a=i(i.bind);e.exports=function(e,t){return o(e),void 0===t?e:s?a(e,t):function(){return e.apply(t,arguments)}}},6097:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=a(t,function(e,i){if(r(e,i,t))return{key:i}},!0);return i&&i.key}})},6119:(e,t,r)=>{"use strict";var i=r(5745),o=r(3392),s=i("keys");e.exports=function(e){return s[e]||(s[e]=o(e))}},6145:e=>{"use strict";e.exports=e=>{let t=e.length,r=43,i=(e=r)=>isNaN(e);if(r+1>t||i()||(r+=1+e[r],r+2>t||i())||(r+=2+e.readInt16BE(r),r+1>t||i())||(r+=1+e[r],r+2>t||i()))return null;let o=e.readInt16BE(r);r+=2;let s=r+o;if(s>t||i(s))return null;for(t=s;r+4<=t||i();){let o=e.readInt16BE(r),s=e.readInt16BE(r+2);if(r+=4,0!==o){r+=s;continue}if(r+2>t||i())break;let a=e.readInt16BE(r);if(r+=2,r+a>t)break;for(;r+3<=t||i();){let o=e[r],s=e.readInt16BE(r+1);if(r+=3,0!==o){r+=s;continue}if(r+s>t||i())return null;return e.toString("ascii",r,r+s)}}return null}},6167:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(6043),c=r(1103),u=r(2652);i({target:"Promise",stat:!0,forced:r(537)},{allSettled:function(e){var t=this,r=a.f(t),i=r.resolve,l=r.reject,f=c(function(){var r=s(t.resolve),a=[],c=0,l=1;u(e,function(e){var s=c++,u=!1;l++,o(r,t,e).then(function(e){!u&&(u=!0,a[s]={status:"fulfilled",value:e},--l||i(a))},function(e){!u&&(u=!0,a[s]={status:"rejected",reason:e},--l||i(a))})}),--l||i(a)});return f.error&&l(f.value),r.promise}})},6187:(e,t,r)=>{"use strict";let{format:i}=r(9023);e.exports=function(){let e={},t=new Map;return{create:function(r,o,s){if(!r)throw Error("Warning name must not be empty");if(!o)throw Error("Warning code must not be empty");if(!s)throw Error("Warning message must not be empty");if(void 0!==e[o=o.toUpperCase()])throw Error(`The code '${o}' already exist`);return t.set(o,!1),e[o]=function(e,t,a){let c;return c=e&&t&&a?i(s,e,t,a):e&&t?i(s,e,t):e?i(s,e):s,{code:o,name:r,message:c}},e[o]},emit:function(r,i,o,s){if(void 0===e[r])throw Error(`The code '${r}' does not exist`);if(!0===t.get(r))return;t.set(r,!0);let a=e[r](i,o,s);process.emitWarning(a.message,a.name,a.code)},emitted:t}}},6193:(e,t,r)=>{"use strict";var i=r(9504),o=Error,s=i("".replace),a=String(new o("zxcasd").stack),c=/\n\s*at [^:]*:[^\n]*/,u=c.test(a);e.exports=function(e,t){if(u&&"string"==typeof e&&!o.prepareStackTrace)for(;t--;)e=s(e,c,"");return e}},6194:(e,t,r)=>{"use strict";var i=r(2248).has;e.exports=function(e){return i(e),e}},6198:(e,t,r)=>{"use strict";var i=r(8014);e.exports=function(e){return i(e.length)}},6215:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(4204);i({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){return o(a,this,s(e))}})},6218:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(){super('You must install "yt-dlp" before using the "ytdlp" source.'),this.name="YtDlpNotInstalled"}}},6223:(e,t,r)=>{"use strict";var i=r(9504),o=r(507),s=r(2248),a=s.Map,c=s.proto,u=i(c.forEach),l=i(c.entries),f=l(new a).next;e.exports=function(e,t,r){return r?o({iterator:l(e),next:f},function(e){return t(e[1],e[0])}):u(e,t)}},6238:(e,t,r)=>{"use strict";var i=r(113).F.ERR_STREAM_PREMATURE_CLOSE;function o(){}e.exports=function e(t,r,s){if("function"==typeof r)return e(t,null,r);r||(r={}),a=s||o,c=!1,s=function(){if(!c){c=!0;for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];a.apply(this,t)}};var a,c,u=r.readable||!1!==r.readable&&t.readable,l=r.writable||!1!==r.writable&&t.writable,f=function(){t.writable||h()},p=t._writableState&&t._writableState.finished,h=function(){l=!1,p=!0,u||s.call(t)},d=t._readableState&&t._readableState.endEmitted,y=function(){u=!1,d=!0,l||s.call(t)},g=function(e){s.call(t,e)},m=function(){var e;return u&&!d?(t._readableState&&t._readableState.ended||(e=new i),s.call(t,e)):l&&!p?(t._writableState&&t._writableState.ended||(e=new i),s.call(t,e)):void 0},b=function(){t.req.on("finish",h)};return t.setHeader&&"function"==typeof t.abort?(t.on("complete",h),t.on("abort",m),t.req?b():t.on("request",b)):l&&!t._writableState&&(t.on("end",f),t.on("close",f)),t.on("end",y),t.on("finish",h),!1!==r.error&&t.on("error",g),t.on("close",m),function(){t.removeListener("complete",h),t.removeListener("abort",m),t.removeListener("request",b),t.req&&t.req.removeListener("finish",h),t.removeListener("end",f),t.removeListener("close",f),t.removeListener("finish",h),t.removeListener("end",y),t.removeListener("error",g),t.removeListener("close",m)}}},6249:(e,t,r)=>{"use strict";var i=r(9039),o=r(6980);e.exports=!i(function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",o(1,7)),7!==e.stack)})},6269:e=>{"use strict";e.exports={}},6270:e=>{"use strict";function t(e,t,r,i){function o(){let e=t.deref();void 0!==e&&r(e,i)}e[i]=o,process.once(i,o)}let r=new FinalizationRegistry(o),i=new WeakMap;function o(e){process.removeListener("exit",e.exit),process.removeListener("beforeExit",e.beforeExit)}e.exports={register:function(e,o){if(void 0===e)throw Error("the object can't be undefined");let s=new WeakRef(e),a={};i.set(e,a),r.register(e,a),t(a,s,o,"exit"),t(a,s,o,"beforeExit")},unregister:function(e){let t=i.get(e);i.delete(e),t&&o(t),r.unregister(e)}}},6279:(e,t,r)=>{"use strict";var i=r(6840);e.exports=function(e,t,r){for(var o in t)i(e,o,t[o],r);return e}},6280:(e,t,r)=>{"use strict";var i=r(6518),o=r(4576),s=r(8745),a=r(4601),c="WebAssembly",u=o[c],l=7!==Error("e",{cause:7}).cause,f=function(e,t){var r={};r[e]=a(e,t,l),i({global:!0,constructor:!0,arity:1,forced:l},r)},p=function(e,t){if(u&&u[e]){var r={};r[e]=a(c+"."+e,t,l),i({target:c,stat:!0,constructor:!0,arity:1,forced:l},r)}};f("Error",function(e){return function(t){return s(e,this,arguments)}}),f("EvalError",function(e){return function(t){return s(e,this,arguments)}}),f("RangeError",function(e){return function(t){return s(e,this,arguments)}}),f("ReferenceError",function(e){return function(t){return s(e,this,arguments)}}),f("SyntaxError",function(e){return function(t){return s(e,this,arguments)}}),f("TypeError",function(e){return function(t){return s(e,this,arguments)}}),f("URIError",function(e){return function(t){return s(e,this,arguments)}}),p("CompileError",function(e){return function(t){return s(e,this,arguments)}}),p("LinkError",function(e){return function(t){return s(e,this,arguments)}}),p("RuntimeError",function(e){return function(t){return s(e,this,arguments)}})},6306:e=>{"use strict";e.exports=function(e={}){let{ERR_PATHS_MUST_BE_STRINGS:t=()=>"fast-redact - Paths must be (non-empty) strings",ERR_INVALID_PATH:r=e=>`fast-redact – Invalid path (${e})`}=e;return function({paths:e}){e.forEach(e=>{if("string"!=typeof e)throw Error(t());try{if(/〇/.test(e))throw Error();let t=("["===e[0]?"":".")+e.replace(/^\*/,"〇").replace(/\.\*/g,".〇").replace(/\[\*\]/g,"[〇]");if(/\n|\r|;/.test(t)||/\/\*/.test(t))throw Error();Function(`
            'use strict'
            const o = new Proxy({}, { get: () => o, set: () => { throw Error() } });
            const 〇 = null;
            o${t}
            if ([o${t}].length !== 1) throw Error()`)()}catch(t){throw Error(r(e))}})}}},6319:(e,t,r)=>{"use strict";var i=r(8551),o=r(9539);e.exports=function(e,t,r,s){try{return s?t(i(r)[0],r[1]):t(r)}catch(t){o(e,"throw",t)}}},6395:e=>{"use strict";e.exports=!1},6437:(e,t,r)=>{"use strict";let i=r(5026),o=r(8409),s=r(7079);e.exports={err:i,mapHttpRequest:o.mapHttpRequest,mapHttpResponse:s.mapHttpResponse,req:o.reqSerializer,res:s.resSerializer,wrapErrorSerializer:function(e){return e===i?e:function(t){return e(i(t))}},wrapRequestSerializer:function(e){return e===o.reqSerializer?e:function(t){return e(o.reqSerializer(t))}},wrapResponseSerializer:function(e){return e===s.resSerializer?e:function(t){return e(s.resSerializer(t))}}}},6518:(e,t,r)=>{"use strict";var i=r(4576),o=r(7347).f,s=r(6699),a=r(6840),c=r(9433),u=r(7740),l=r(2796);e.exports=function(e,t){var r,f,p,h,d,y=e.target,g=e.global,m=e.stat;if(r=g?i:m?i[y]||c(y,{}):i[y]&&i[y].prototype)for(f in t){if(h=t[f],p=e.dontCallGetSet?(d=o(r,f))&&d.value:r[f],!l(g?f:y+(m?".":"#")+f,e.forced)&&void 0!==p){if(typeof h==typeof p)continue;u(h,p)}(e.sham||p&&p.sham)&&s(h,"sham",!0),a(r,f,h,e)}}},6526:(e,t,r)=>{"use strict";let i=r(793),{mapHttpRequest:o,mapHttpResponse:s}=r(6437),a=r(809),c=r(8463),{lsCacheSym:u,chindingsSym:l,parsedChindingsSym:f,writeSym:p,serializersSym:h,formatOptsSym:d,endSym:y,stringifiersSym:g,stringifySym:m,wildcardFirstSym:b,needsMetadataGsym:v,redactFmtSym:w,streamSym:_,nestedKeySym:S,formattersSym:x,messageKeySym:E}=r(544);function O(){}function k(e,t,i,o){if(t&&"function"==typeof t)return j((t=t.bind(o))(e),i,e);try{let t=r(1809).prettyFactory||r(1809);return t.asMetaWrapper=j,j(t(e),i,e)}catch(e){if(e.message.startsWith("Cannot find module 'pino-pretty'"))throw Error("Missing `pino-pretty` module: `pino-pretty` must be installed separately");throw e}}function j(e,t,r){r=Object.assign({suppressFlushSyncWarning:!1},r);let i=!1;return{[v]:!0,lastLevel:0,lastMsg:null,lastObj:null,lastLogger:null,flushSync(){r.suppressFlushSyncWarning||i||(i=!0,R(t,this),t.write(e(Object.assign({level:40,msg:"pino.final with prettyPrint does not support flushing",time:Date.now()},this.chindings()))))},chindings(){let e=this.lastLogger,t=null;return e?(e.hasOwnProperty(f)?t=e[f]:(t=JSON.parse("{"+e[l].substr(1)+"}"),e[f]=t),t):null},write(r){let i=this.lastLogger,o=this.chindings(),s=this.lastTime;s=s.match(/^\d+/)?parseInt(s):s.slice(1,-1);let a=this.lastObj,c=this.lastMsg,u=i[x],l=u.log?u.log(a):a,f=i[E];c&&l&&!l.hasOwnProperty(f)&&(l[f]=c);let p=Object.assign({level:this.lastLevel,time:s},l,null),d=i[h],y=Object.keys(d);for(var m=0;m<y.length;m++){let e=y[m];void 0!==p[e]&&(p[e]=d[e](p[e]))}for(let e in o)p.hasOwnProperty(e)||(p[e]=o[e]);let b=i[g][w],v=e("function"==typeof b?b(p):p);void 0!==v&&(R(t,this),t.write(v))}}}function T(e){let t=new a(e);return t.on("error",function e(r){if("EPIPE"===r.code){t.write=O,t.end=O,t.flushSync=O,t.destroy=O;return}t.removeListener("error",e),t.emit("error",r)}),t}function R(e,t){!0===e[v]&&(e.lastLevel=t.lastLevel,e.lastMsg=t.lastMsg,e.lastObj=t.lastObj,e.lastTime=t.lastTime,e.lastLogger=t.lastLogger)}e.exports={noop:O,buildSafeSonicBoom:T,getPrettyStream:k,asChindings:function(e,t){let r,i=e[l],o=e[m],s=e[g],a=s[b],c=e[h];for(let u in t=(0,e[x].bindings)(t))if(r=t[u],!0===("level"!==u&&"serializers"!==u&&"formatters"!==u&&"customLevels"!==u&&t.hasOwnProperty(u)&&void 0!==r)){if(r=c[u]?c[u](r):r,void 0===(r=(s[u]||a||o)(r)))continue;i+=',"'+u+'":'+r}return i},asJson:function(e,t,r,i){let o,s=this[m],a=this[g],c=this[y],f=this[l],p=this[h],d=this[x],v=this[E],w=this[u][r]+i;w+=f;let _=void 0===e.hasOwnProperty;d.log&&(e=d.log(e)),void 0!==t&&(e[v]=t);let S=a[b];for(let t in e)if(o=e[t],(_||e.hasOwnProperty(t))&&void 0!==o){o=p[t]?p[t](o):o;let e=a[t]||S;switch(typeof o){case"undefined":case"function":continue;case"number":!1===Number.isFinite(o)&&(o=null);case"boolean":e&&(o=e(o));break;case"string":o=(e||function(e){let t="",r=0,i=!1,o=255,s=e.length;if(s>100)return JSON.stringify(e);for(var a=0;a<s&&o>=32;a++)(34===(o=e.charCodeAt(a))||92===o)&&(t+=e.slice(r,a)+"\\",r=a,i=!0);return i?t+=e.slice(r):t=e,o<32?JSON.stringify(e):'"'+t+'"'})(o);break;default:o=(e||s)(o)}if(void 0===o)continue;w+=',"'+t+'":'+o}return w+c},genLog:function(e,t){if(!t)return r;return function(...i){t.call(this,i,r,e)};function r(t,...a){if("object"==typeof t){let r,c=t;null!==t&&(t.method&&t.headers&&t.socket?t=o(t):"function"==typeof t.setHeader&&(t=s(t))),this[S]&&(t={[this[S]]:t}),null===c&&0===a.length?r=[null]:(c=a.shift(),r=a),this[p](t,i(c,r,this[d]),e)}else this[p](null,i(t,a,this[d]),e)}},createArgsNormalizer:function(e){return function(t,r={},i){var o;if("string"==typeof r?(i=T({dest:r,sync:!0}),r={}):"string"==typeof i?i=T({dest:i,sync:!0}):(r instanceof a||r.writable||r._writableState)&&(i=r,r=null),"extreme"in(r=Object.assign({},e,r)))throw Error("The extreme option has been removed, use pino.destination({ sync: false }) instead");if("onTerminated"in r)throw Error("The onTerminated option has been removed, use pino.final instead");"changeLevelName"in r&&(process.emitWarning("The changeLevelName option is deprecated and will be removed in v7. Use levelKey instead.",{code:"changeLevelName_deprecation"}),r.levelKey=r.changeLevelName,delete r.changeLevelName);let{enabled:s,prettyPrint:c,prettifier:u,messageKey:l}=r;return!1===s&&(r.level="silent"),(i=i||process.stdout)===process.stdout&&i.fd>=0&&(o=i).write===o.constructor.prototype.write&&(i=T({fd:i.fd,sync:!0})),c&&(i=k(Object.assign({messageKey:l},c),u,i,t)),{opts:r,stream:i}}},final:function(e,t){if(void 0===e||"function"!=typeof e.child)throw Error("expected a pino logger instance");let r=void 0!==t;if(r&&"function"!=typeof t)throw Error("if supplied, the handler parameter should be a function");let i=e[_];if("function"!=typeof i.flushSync)throw Error("final requires a stream that has a flushSync method, such as pino.destination");let o=new Proxy(e,{get:(e,t)=>t in e.levels.values?(...r)=>{e[t](...r),i.flushSync()}:e[t]});return r?(e=null,...r)=>{try{i.flushSync()}catch(e){}return t(e,o,...r)}:o},stringify:function(e){try{return JSON.stringify(e)}catch(t){return c(e)}},buildFormatters:function(e,t,r){return{level:e,bindings:t,log:r}}}},6532:(e,t,r)=>{"use strict";function i(e,t,r,i,o,s,a){try{var c=e[s](a),u=c.value}catch(e){r(e);return}c.done?t(u):Promise.resolve(u).then(i,o)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}var s=r(113).F.ERR_INVALID_ARG_TYPE;e.exports=function(e,t,r){if(t&&"function"==typeof t.next)a=t;else if(t&&t[Symbol.asyncIterator])a=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])a=t[Symbol.iterator]();else throw new s("iterable",["Iterable"],t);var a,c=new e(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var i,o,s;i=e,o=t,s=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(o))in i?Object.defineProperty(i,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[o]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({objectMode:!0},r)),u=!1;function l(){return f.apply(this,arguments)}function f(){var e;return e=function*(){try{var e=yield a.next(),t=e.value;e.done?c.push(null):c.push((yield t))?l():u=!1}catch(e){c.destroy(e)}},(f=function(){var t=this,r=arguments;return new Promise(function(o,s){var a=e.apply(t,r);function c(e){i(a,o,s,c,u,"next",e)}function u(e){i(a,o,s,c,u,"throw",e)}c(void 0)})}).apply(this,arguments)}return c._read=function(){u||(u=!0,l())},c}},6587:e=>{e.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!=typeof t)throw TypeError("need wrapper function");return Object.keys(t).forEach(function(e){i[e]=t[e]}),i;function i(){for(var e=Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var i=t.apply(this,e),o=e[e.length-1];return"function"==typeof i&&i!==o&&Object.keys(o).forEach(function(e){i[e]=o[e]}),i}}},6611:(e,t,r)=>{var i=r(3519),o=function(){},s=global.Bare?queueMicrotask:process.nextTick.bind(process),a=function(e,t,r){if("function"==typeof t)return a(e,null,t);t||(t={}),r=i(r||o);var c=e._writableState,u=e._readableState,l=t.readable||!1!==t.readable&&e.readable,f=t.writable||!1!==t.writable&&e.writable,p=!1,h=function(){e.writable||d()},d=function(){f=!1,l||r.call(e)},y=function(){l=!1,f||r.call(e)},g=function(t){r.call(e,t?Error("exited with error code: "+t):null)},m=function(t){r.call(e,t)},b=function(){s(v)},v=function(){if(!p&&(l&&!(u&&u.ended&&!u.destroyed)||f&&!(c&&c.ended&&!c.destroyed)))return r.call(e,Error("premature close"))},w=function(){e.req.on("finish",d)};return e.setHeader&&"function"==typeof e.abort?(e.on("complete",d),e.on("abort",b),e.req?w():e.on("request",w)):f&&!c&&(e.on("end",h),e.on("close",h)),e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length&&e.on("exit",g),e.on("end",y),e.on("finish",d),!1!==t.error&&e.on("error",m),e.on("close",b),function(){p=!0,e.removeListener("complete",d),e.removeListener("abort",b),e.removeListener("request",w),e.req&&e.req.removeListener("finish",d),e.removeListener("end",h),e.removeListener("close",h),e.removeListener("finish",d),e.removeListener("exit",g),e.removeListener("end",y),e.removeListener("error",m),e.removeListener("close",b)}};e.exports=a},6639:(e,t,r)=>{"use strict";var i=r(9565),o=r(9306),s=r(8551),a=r(34),c=r(6837),u=r(7751),l=r(1767),f=r(772),p=function(e){var t=0===e,r=1===e,p=2===e,h=3===e;return function(e,d,y){s(e);var g=void 0!==d;(g||!t)&&o(d);var m=l(e),b=u("Promise"),v=m.iterator,w=m.next,_=0;return new b(function(e,o){var u=function(e){f(v,o,e,o)},l=function(){try{if(g)try{c(_)}catch(e){u(e)}b.resolve(s(i(w,v))).then(function(i){try{if(s(i).done)t?(y.length=_,e(y)):e(!h&&(p||void 0));else{var c=i.value;try{if(g){var m=d(c,_),w=function(i){if(r)l();else if(p)i?l():f(v,e,!1,o);else if(t)try{y[_++]=i,l()}catch(e){u(e)}else i?f(v,e,h||c,o):l()};a(m)?b.resolve(m).then(w,u):w(m)}else y[_++]=c,l()}catch(e){u(e)}}}catch(e){o(e)}},o)}catch(e){o(e)}};l()})}};e.exports={toArray:p(0),forEach:p(1),every:p(2),some:p(3),find:p(4)}},6694:(e,t,r)=>{"use strict";let i=r(5317),{logScope:o}=r(5469),s=r(5685),a=o("spawn");e.exports={spawnStdout:async function(e,t=[]){return new Promise((r,o)=>{let c=0,u=0,l=Buffer.alloc(5e6),f=Buffer.alloc(5e6),p=i.spawn(e,t);p.on("spawn",()=>{a.info(`running ${e} ${t.join(" ")}`)}),p.on("error",e=>o(e)),p.on("close",t=>{0!==t?o(new s(e,t)):(a.debug(`process ${e} exited successfully`),r({stdout:l.slice(0,c),stderr:f.slice(0,u)}))}),p.stdout.on("data",e=>{c+=e.copy(l,c)}),p.stderr.on("data",t=>{a.warn(`[${e}][stderr] ${t}`),u+=t.copy(f,u)})})}}},6698:e=>{"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},6699:(e,t,r)=>{"use strict";var i=r(3724),o=r(4913),s=r(6980);e.exports=i?function(e,t,r){return o.f(e,t,s(1,r))}:function(e,t,r){return e[t]=r,e}},6706:(e,t,r)=>{"use strict";var i=r(9504),o=r(9306);e.exports=function(e,t,r){try{return i(o(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},6708:(e,t,r)=>{"use strict";function i(e){var t=this;this.next=null,this.entry=null,this.finish=function(){var r=t,i=e,o=r.entry;for(r.entry=null;o;){var s=o.callback;i.pendingcb--,s(void 0),o=o.next}i.corkedRequestsFree.next=r}}e.exports=O,O.WritableState=E;var o,s,a={deprecate:r(7983)},c=r(1416),u=r(181).Buffer,l=("undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},f=r(5896),p=r(5291).getHighWaterMark,h=r(113).F,d=h.ERR_INVALID_ARG_TYPE,y=h.ERR_METHOD_NOT_IMPLEMENTED,g=h.ERR_MULTIPLE_CALLBACK,m=h.ERR_STREAM_CANNOT_PIPE,b=h.ERR_STREAM_DESTROYED,v=h.ERR_STREAM_NULL_VALUES,w=h.ERR_STREAM_WRITE_AFTER_END,_=h.ERR_UNKNOWN_ENCODING,S=f.errorOrDestroy;function x(){}function E(e,t,s){o=o||r(5382),e=e||{},"boolean"!=typeof s&&(s=t instanceof o),this.objectMode=!!e.objectMode,s&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=p(this,e,"writableHighWaterMark",s),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var a=!1===e.decodeStrings;this.decodeStrings=!a,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,i=r.sync,o=r.writecb;if("function"!=typeof o)throw new g;if(r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,t)--r.pendingcb,i?(process.nextTick(o,t),process.nextTick(L,e,r),e._writableState.errorEmitted=!0,S(e,t)):(o(t),e._writableState.errorEmitted=!0,S(e,t),L(e,r));else{var s=R(r)||e.destroyed;s||r.corked||r.bufferProcessing||!r.bufferedRequest||T(e,r),i?process.nextTick(j,e,r,s,o):j(e,r,s,o)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==e.emitClose,this.autoDestroy=!!e.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}r(2017)(O,c),E.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t};try{Object.defineProperty(E.prototype,"buffer",{get:a.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}function O(e){var t=this instanceof(o=o||r(5382));if(!t&&!s.call(O,this))return new O(e);this._writableState=new E(e,this,t),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),c.call(this)}function k(e,t,r,i,o,s,a){t.writelen=i,t.writecb=a,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new b("write")):r?e._writev(o,t.onwrite):e._write(o,s,t.onwrite),t.sync=!1}function j(e,t,r,i){var o,s;r||(o=e,0===(s=t).length&&s.needDrain&&(s.needDrain=!1,o.emit("drain"))),t.pendingcb--,i(),L(e,t)}function T(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var o=Array(t.bufferedRequestCount),s=t.corkedRequestsFree;s.entry=r;for(var a=0,c=!0;r;)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,k(e,t,!0,t.length,o,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new i(t),t.bufferedRequestCount=0}else{for(;r;){var u=r.chunk,l=r.encoding,f=r.callback,p=t.objectMode?1:u.length;if(k(e,t,!1,p,u,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function R(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function P(e,t){e._final(function(r){t.pendingcb--,r&&S(e,r),t.prefinished=!0,e.emit("prefinish"),L(e,t)})}function L(e,t){var r=R(t);if(r&&(t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,process.nextTick(P,e,t))),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var i=e._readableState;(!i||i.autoDestroy&&i.endEmitted)&&e.destroy()}return r}"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(s=Function.prototype[Symbol.hasInstance],Object.defineProperty(O,Symbol.hasInstance,{value:function(e){return!!s.call(this,e)||this===O&&e&&e._writableState instanceof E}})):s=function(e){return e instanceof this},O.prototype.pipe=function(){S(this,new m)},O.prototype.write=function(e,t,r){var i,o,s,a,c,f,p,h=this._writableState,y=!1,g=!h.objectMode&&(i=e,u.isBuffer(i)||i instanceof l);return(g&&!u.isBuffer(e)&&(o=e,e=u.from(o)),"function"==typeof t&&(r=t,t=null),g?t="buffer":t||(t=h.defaultEncoding),"function"!=typeof r&&(r=x),h.ending)?(s=r,S(this,a=new w),process.nextTick(s,a)):(g||(c=e,f=r,null===c?p=new v:"string"==typeof c||h.objectMode||(p=new d("chunk",["string","Buffer"],c)),!p||(S(this,p),process.nextTick(f,p),0)))&&(h.pendingcb++,y=function(e,t,r,i,o,s){if(!r){var a,c,l=(a=i,c=o,t.objectMode||!1===t.decodeStrings||"string"!=typeof a||(a=u.from(a,c)),a);i!==l&&(r=!0,o="buffer",i=l)}var f=t.objectMode?1:i.length;t.length+=f;var p=t.length<t.highWaterMark;if(p||(t.needDrain=!0),t.writing||t.corked){var h=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:o,isBuf:r,callback:s,next:null},h?h.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else k(e,t,!1,f,i,o,s);return p}(this,h,g,e,t,r)),y},O.prototype.cork=function(){this._writableState.corked++},O.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||T(this,e))},O.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new _(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(O.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(O.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),O.prototype._write=function(e,t,r){r(new y("_write()"))},O.prototype._writev=null,O.prototype.end=function(e,t,r){var i,o,s,a=this._writableState;return"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),a.corked&&(a.corked=1,this.uncork()),a.ending||(i=this,o=a,s=r,o.ending=!0,L(i,o),s&&(o.finished?process.nextTick(s):i.once("finish",s)),o.ended=!0,i.writable=!1),this},Object.defineProperty(O.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(O.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),O.prototype.destroy=f.destroy,O.prototype._undestroy=f.undestroy,O.prototype._destroy=function(e,t){t(e)}},6754:e=>{"use strict";e.exports=function(e){let{secret:t,censor:r,compileRestore:i,serialize:o,groupRedact:s,nestedRedact:a,wildcards:c,wcLen:u}=e,l=[{secret:t,censor:r,compileRestore:i}];return!1!==o&&l.push({serialize:o}),u>0&&l.push({groupRedact:s,nestedRedact:a,wildcards:c,wcLen:u}),Object.assign(...l)}},6771:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(8750);i({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){return o(a,this,s(e))}})},6801:(e,t,r)=>{"use strict";var i=r(3724),o=r(8686),s=r(4913),a=r(8551),c=r(5397),u=r(1072);t.f=i&&!o?Object.defineProperties:function(e,t){a(e);for(var r,i=c(t),o=u(t),l=o.length,f=0;l>f;)s.f(e,r=o[f++],i[r]);return e}},6823:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},6837:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>0x1fffffffffffff)throw t("Maximum allowed index exceeded");return e}},6840:(e,t,r)=>{"use strict";var i=r(4901),o=r(4913),s=r(283),a=r(9433);e.exports=function(e,t,r,c){c||(c={});var u=c.enumerable,l=void 0!==c.name?c.name:t;if(i(r)&&s(r,l,c),c.global)u?e[t]=r:a(t,r);else{try{c.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=r:o.f(e,t,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return e}},6900:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4905),r(8872),r(8992),r(8159),r(7550),r(4114),r(1393),r(1454);let i=r(9664),{getManagedCacheStorage:o}=r(3971),s=(e,t)=>Object.keys(e).reduce((r,i)=>Object.assign(r,t.includes(i)&&{[i]:e[i]}),{}),a=e=>{try{let r=s(e,["id","name","alias","duration"]);if(r.name=(r.name||"").replace(/（\s*cover[:：\s][^）]+）/i,"").replace(/\(\s*cover[:：\s][^)]+\)/i,"").replace(/（\s*翻自[:：\s][^）]+）/,"").replace(/\(\s*翻自[:：\s][^)]+\)/,""),r.album=s(e.album,["id","name"]),r.artists=e.artists.map(e=>s(e,["id","name"])),r.keyword=r.name+" - "+(e=>{let t=[e[0]];return e.slice(1).some(e=>t.reduce((e,t)=>e+t.length,0)>15||(t.push(e),!1)),t})(r.artists.map(e=>e.name)).join(" / "),"true"===process.env.SEARCH_ALBUM){var t;let e=null==(t=r.album)?void 0:t.name;e&&e!==r.name&&(r.keyword+=` ${e}`)}return r}catch(e){return console.log("getFormatData err: ",e),{}}},c=(e,t)=>{if(!t)return i("GET","https://music.163.com/api/song/detail?ids=["+e+"]").then(e=>e.json()).then(e=>{if(e&&e.songs&&e.songs.length){let t=a(e.songs[0]);return t.name?t:Promise.reject()}return Promise.reject()});{let e=a(t);return e.name?Promise.resolve(e):Promise.reject()}},u=o("provider/find");e.exports=(e,t)=>t?c(e,t):u.cache(e,()=>c(e))},6928:e=>{"use strict";e.exports=require("path")},6955:(e,t,r)=>{"use strict";var i=r(2140),o=r(4901),s=r(2195),a=r(8227)("toStringTag"),c=Object,u="Arguments"===s(function(){return arguments}()),l=function(e,t){try{return e[t]}catch(e){}};e.exports=i?s:function(e){var t,r,i;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=l(t=c(e),a))?r:u?s(t):"Object"===(i=s(t))&&o(t.callee)?"Arguments":i}},6969:(e,t,r)=>{"use strict";var i=r(2777),o=r(757);e.exports=function(e){var t=i(e,"string");return o(t)?t:t+""}},6980:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},6982:e=>{"use strict";e.exports=require("crypto")},7016:e=>{"use strict";e.exports=require("url")},7040:(e,t,r)=>{"use strict";e.exports=r(4495)&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(e,t,r)=>{"use strict";var i=r(9504),o=r(9039),s=r(2195),a=Object,c=i("".split);e.exports=o(function(){return!a("z").propertyIsEnumerable(0)})?function(e){return"String"===s(e)?c(e,""):a(e)}:a},7079:e=>{"use strict";e.exports={mapHttpResponse:function(e){return{res:i(e)}},resSerializer:i};let t=Symbol("pino-raw-res-ref"),r=Object.create({},{statusCode:{enumerable:!0,writable:!0,value:0},headers:{enumerable:!0,writable:!0,value:""},raw:{enumerable:!1,get:function(){return this[t]},set:function(e){this[t]=e}}});function i(e){let t=Object.create(r);return t.statusCode=e.statusCode,t.headers=e.getHeaders?e.getHeaders():e._headers,t.raw=e,t}Object.defineProperty(r,t,{writable:!0,value:{}})},7080:(e,t,r)=>{"use strict";var i=r(4402).has;e.exports=function(e){return i(e),e}},7145:(e,t,r)=>{"use strict";var i=r(6518),o=r(1625),s=r(2787),a=r(2967),c=r(7740),u=r(2360),l=r(6699),f=r(6980),p=r(7584),h=r(747),d=r(2652),y=r(2603),g=r(8227)("toStringTag"),m=Error,b=[].push,v=function(e,t){var r,i=o(w,this);a?r=a(new m,i?s(this):w):l(r=i?this:u(w),g,"Error"),void 0!==t&&l(r,"message",y(t)),h(r,v,r.stack,1),arguments.length>2&&p(r,arguments[2]);var c=[];return d(e,b,{that:c}),l(r,"errors",c),r};a?a(v,m):c(v,m,{name:!0});var w=v.prototype=u(m.prototype,{constructor:f(1,v),message:f(1,""),name:f(1,"AggregateError")});i({global:!0,constructor:!0,arity:2},{AggregateError:v})},7157:e=>{"use strict";function t(e,t){return null!=e&&("hasOwn"in Object?Object.hasOwn(e,t):Object.prototype.hasOwnProperty.call(e,t))}function r(e,t){for(var r=-1,i=t.length,o=e;null!=o&&++r<i;)o=o[t[r]];return o}function i(e,t,r){if(e.depth===r)return i(e.parent,t,r);var o={parent:e,key:t,depth:r,children:[]};return e.children.push(o),o}function o(e,t,r){let i=e,o=[];do o.push(i.key),i=i.parent;while(null!=i.parent)return{path:o,value:t,target:r}}e.exports={groupRedact:function(e,t,i,o,s){let a=r(e,t);if(null==a||"string"==typeof a)return{keys:null,values:null,target:a,flat:!0};let c=Object.keys(a),u=c.length,l=t.length,f=s?[...t]:void 0,p=Array(u);for(var h=0;h<u;h++){let e=c[h];p[h]=a[e],s?(f[l]=e,a[e]=i(a[e],f)):o?a[e]=i(a[e]):a[e]=i}return{keys:c,values:p,target:a,flat:!0}},groupRestore:function({keys:e,values:t,target:r}){if(null==r||"string"==typeof r)return;let i=e.length;for(var o=0;o<i;o++)r[e[o]]=t[o]},nestedRedact:function(e,s,a,c,u,l,f){let p=r(s,a);if(null==p)return;let h=Object.keys(p),d=h.length;for(var y=0;y<d;y++)!function(e,r,s,a,c,u,l,f){let p=c.length,h=p-1,d=s;var y,g,m,b,v,w=-1,_=null,S=!1,x=0,E={parent:null,key:null,children:[],depth:0};if(m=y=r[s],"object"==typeof y){for(;null!=y&&++w<p&&(x+=1,s=c[w],"*"===s||_||"object"==typeof y&&s in y);)if("*"!==s||("*"===_&&(S=!0),_=s,w===h)){if(_){let p=Object.keys(y);for(var O=0;O<p.length;O++){let _=p[O];if(v=y[_],b="*"===s,S)E=i(E,_,x),m=function e(r,s,a,c,u,l,f,p,h,d,y,g,m,b,v,w,_,S,x,E){if(0===s&&(m||"object"==typeof r&&null!==r&&a in r))if(g=m?r:r[a],y=v!==w?g:f?p?l(g,[...c,h,...u]):l(g):l,m){let e=o(_,g,x);S.push(e),d[b]=y}else if(r[a]===y);else if(void 0===y&&void 0!==l||t(r,a)&&y===g);else{let e=o(i(_,a,E+1),g,x);S.push(e),r[a]=y}for(let t in r)"object"==typeof r[t]&&(_=i(_,t,E),e(r[t],s-1,a,c,u,l,f,p,h,d,y,g,m,b,v,w,_,S,x,E+1))}(v,w-1,s,a,c,u,l,f,d,y,g,m,b,_,w,h,E,e,r[d],x+1);else if(b||"object"==typeof v&&null!==v&&s in v)if(m=b?v:v[s],g=w!==h?m:l?f?u(m,[...a,d,...c]):u(m):u,b){let t=o(i(E,_,x),m,r[d]);e.push(t),y[_]=g}else if(v[s]===g);else if(void 0===g&&void 0!==u||t(v,s)&&g===m)E=i(E,_,x);else{E=i(E,_,x);let t=o(i(E,s,x+1),m,r[d]);e.push(t),v[s]=g}}_=null}else{if(m=y[s],E=i(E,s,x),g=w!==h?m:l?f?u(m,[...a,d,...c]):u(m):u,t(y,s)&&g===m||void 0===g&&void 0!==u);else{let t=o(E,m,r[d]);e.push(t),y[s]=g}y=y[s]}if("object"!=typeof y)break}}}(e,p,h[y],a,c,u,l,f);return e},nestedRestore:function(e){for(let t=0;t<e.length;t++){let{target:r,path:i,value:o}=e[t],s=r;for(let e=i.length-1;e>0;e--)s=s[i[e]];s[i[0]]=o}}}},7268:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0);return!0===a(t,function(e,i){if(r(e,i,t))return!0},!0)}})},7273:(e,t,r)=>{"use strict";var i=r(6518),o=r(3317),s=r(6194),a=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===a(s(this),function(t){if(o(t,e))return!0},!0)}})},7333:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(9306),a=r(8551),c=r(34),u=r(1767),l=r(2059),f=r(2529),p=r(772),h=l(function(e){var t=this,r=t.iterator,i=t.predicate;return new e(function(s,u){var l=function(e){t.done=!0,u(e)},h=function(e){p(r,l,e,l)},d=function(){try{e.resolve(a(o(t.next,r))).then(function(r){try{if(a(r).done)t.done=!0,s(f(void 0,!0));else{var o=r.value;try{var u=i(o,t.counter++),p=function(e){e?s(f(o,!1)):d()};c(u)?e.resolve(u).then(p,h):p(u)}catch(e){h(e)}}}catch(e){l(e)}},l)}catch(e){l(e)}};d()})});i({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{filter:function(e){return a(this),s(e),new h(u(this),{predicate:e})}})},7347:(e,t,r)=>{"use strict";var i=r(3724),o=r(9565),s=r(8773),a=r(6980),c=r(5397),u=r(6969),l=r(9297),f=r(5917),p=Object.getOwnPropertyDescriptor;t.f=i?p:function(e,t){if(e=c(e),t=u(t),f)try{return p(e,t)}catch(e){}if(l(e,t))return a(!o(s.f,e,t),e[t])}},7407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(8335);let i=r(2569),o=r(9643),s=r(1600),a=r(9664),{getManagedCacheStorage:c}=r(3971),u={origin:"http://www.joox.com",referer:"http://www.joox.com",cookie:process.env.JOOX_COOKIE||null},l=e=>{let{decode:t}=s.base64;return{id:e.songid,name:t(e.info1||""),duration:1e3*e.playtime,album:{id:e.albummid,name:t(e.info3||"")},artists:e.singer_list.map(({id:e,name:r})=>({id:e,name:t(r||"")}))}},f=e=>a("GET","http://api.joox.com/web-fcgi-bin/web_get_songinfo?songid="+e+"&country=hk&lang=zh_cn&from_type=-1&channel_id=-1&_="+new Date().getTime(),u).then(e=>e.jsonp()).then(e=>{let t=(e.r320Url||e.r192Url||e.mp3Url||e.m4aUrl).replace(/M\d00([\w]+).mp3/,"M800$1.mp3");return t||Promise.reject()}).catch(()=>i().joox.track(e)),p=c("provider/joox");e.exports={check:e=>p.cache(e,()=>(e=>a("GET","http://api-jooxtt.sanook.com/web-fcgi-bin/web_search?country=hk&lang=zh_TW&search_input="+encodeURIComponent((e=>/[\u0800-\u4e00]/.test(e.name)?e.name:e.keyword)(e))+"&sin=0&ein=30",u).then(e=>e.body()).then(t=>{let r=o(JSON.parse(t.replace(/'/g,'"')).itemlist.map(l),e);return r?r.id:Promise.reject()}))(e)).then(f),track:f}},7415:(e,t,r)=>{"use strict";var i=r(6518),o=r(6194),s=r(6223);i({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=s(o(this),function(t,r){if(t===e)return{key:r}},!0);return t&&t.key}})},7476:(e,t,r)=>{"use strict";var i=r(2195),o=r(9504);e.exports=function(e){if("Function"===i(e))return o(e)}},7550:(e,t,r)=>{"use strict";r(3579)},7583:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(2248),c=r(6223),u=a.Map,l=a.set;i({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=new u;return c(t,function(e,o){l(i,o,r(e,o,t))}),i}})},7584:(e,t,r)=>{"use strict";var i=r(34),o=r(6699);e.exports=function(e,t){i(t)&&"cause"in t&&o(e,"cause",t.cause)}},7588:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(2652),a=r(9306),c=r(8551),u=r(1767),l=r(9539),f=r(4549)("forEach",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:f},{forEach:function(e){c(this);try{a(e)}catch(e){l(this,"throw",e)}if(f)return o(f,this,e);var t=u(this),r=0;s(t,function(t){e(t,r++)},{IS_RECORD:!0})}})},7589:(e,t,r)=>{"use strict";let{LEVELS:i,LEVEL_NAMES:o}=r(4160),s=e=>e,a={default:s,60:s,50:s,40:s,30:s,20:s,10:s,message:s,greyMessage:s},{createColors:c}=r(5844),u=c({useColor:!0}),{white:l,bgRed:f,red:p,yellow:h,green:d,blue:y,gray:g,cyan:m}=u,b={default:l,60:f,50:p,40:h,30:d,20:y,10:g,message:m,greyMessage:g};function v(e){return function(t,r,{customLevels:s,customLevelNames:a}={}){let c=e?s||i:Object.assign({},i,s),u=e?a||o:Object.assign({},o,a),l="default";l=Number.isInteger(+t)?Object.prototype.hasOwnProperty.call(c,t)?t:l:Object.prototype.hasOwnProperty.call(u,t.toLowerCase())?u[t.toLowerCase()]:l;let f=c[l];return Object.prototype.hasOwnProperty.call(r,l)?r[l](f):r.default(f)}}e.exports=function(e=!1,t,r){if(e&&void 0!==t){let e=t.reduce(function(e,[t,r]){return e[t]="function"==typeof u[r]?u[r]:l,e},{default:l,message:m,greyMessage:g}),i=r?e:Object.assign({},b,e),o=v(r),s=function(e,t){return o(e,i,t)};return s.message=s.message||i.message,s.greyMessage=s.greyMessage||i.greyMessage,s}if(e){let e=v(r),t=function(t,r){return e(t,b,r)};return t.message=b.message,t.greyMessage=b.greyMessage,t}let i=v(r),o=function(e,t){return i(e,a,t)};return o.message=a.message,o.greyMessage=a.greyMessage,o}},7629:(e,t,r)=>{"use strict";var i=r(6395),o=r(4576),s=r(9433),a="__core-js_shared__",c=e.exports=o[a]||s(a,{});(c.versions||(c.versions=[])).push({version:"3.44.0",mode:i?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7650:(e,t,r)=>{"use strict";var i=r(7751),o=r(4901),s=r(1563),a=r(34),c=i("Set");e.exports=function(e){return a(e)&&"number"==typeof e.size&&o(e.has)&&o(e.keys)?e:s(e)?new c(e):e}},7657:(e,t,r)=>{"use strict";var i,o,s,a=r(9039),c=r(4901),u=r(34),l=r(2360),f=r(2787),p=r(6840),h=r(8227),d=r(6395),y=h("iterator"),g=!1;[].keys&&("next"in(s=[].keys())?(o=f(f(s)))!==Object.prototype&&(i=o):g=!0),!u(i)||a(function(){var e={};return i[y].call(e)!==e})?i={}:d&&(i=l(i)),c(i[y])||p(i,y,function(){return this}),e.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:g}},7740:(e,t,r)=>{"use strict";var i=r(9297),o=r(5031),s=r(7347),a=r(4913);e.exports=function(e,t,r){for(var c=o(t),u=a.f,l=s.f,f=0;f<c.length;f++){var p=c[f];i(e,p)||r&&i(r,p)||u(e,p,l(t,p))}}},7750:(e,t,r)=>{"use strict";var i=r(4117),o=TypeError;e.exports=function(e){if(i(e))throw new o("Can't call method on "+e);return e}},7751:(e,t,r)=>{"use strict";var i=r(4576),o=r(4901);e.exports=function(e,t){var r;return arguments.length<2?o(r=i[e])?r:void 0:i[e]&&i[e][t]}},7758:(e,t,r)=>{"use strict";var i,o=r(113).F,s=o.ERR_MISSING_ARGS,a=o.ERR_STREAM_DESTROYED;function c(e){if(e)throw e}function u(e){e()}function l(e,t){return e.pipe(t)}e.exports=function(){for(var e,t,o=arguments.length,f=Array(o),p=0;p<o;p++)f[p]=arguments[p];var h=(e=f).length&&"function"==typeof e[e.length-1]?e.pop():c;if(Array.isArray(f[0])&&(f=f[0]),f.length<2)throw new s("streams");var d=f.map(function(e,o){var s,c,l,p,y,g,m=o<f.length-1;return s=o>0,l=c=function(e){t||(t=e),e&&d.forEach(u),m||(d.forEach(u),h(t))},p=!1,c=function(){p||(p=!0,l.apply(void 0,arguments))},y=!1,e.on("close",function(){y=!0}),void 0===i&&(i=r(6238)),i(e,{readable:m,writable:s},function(e){if(e)return c(e);y=!0,c()}),g=!1,function(t){if(!y&&!g){if(g=!0,e.setHeader&&"function"==typeof e.abort)return e.abort();if("function"==typeof e.destroy)return e.destroy();c(t||new a("pipe"))}}});return f.reduce(l)}},7814:e=>{"use strict";if("undefined"!=typeof SharedArrayBuffer&&"undefined"!=typeof Atomics){let t=new Int32Array(new SharedArrayBuffer(4));e.exports=function(e){if(!1==(e>0&&e<1/0)){if("number"!=typeof e&&"bigint"!=typeof e)throw TypeError("sleep: ms must be a number");throw RangeError("sleep: ms must be a number that is greater than 0 but less than Infinity")}Atomics.wait(t,0,0,Number(e))}}else e.exports=function(e){if(!1==(e>0&&e<1/0)){if("number"!=typeof e&&"bigint"!=typeof e)throw TypeError("sleep: ms must be a number");throw RangeError("sleep: ms must be a number that is greater than 0 but less than Infinity")}let t=Date.now()+Number(e);for(;t>Date.now(););}},7935:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4905),r(8872),r(8992),r(1393),r(1454),r(9920),r(3949);let i=e=>{let t=BigInt(e);return{low:Number(t),valueOf:()=>t.valueOf(),toString:()=>t.toString(),not:()=>i(~t),isNegative:()=>t<0,or:e=>i(t|BigInt(e)),and:e=>i(t&BigInt(e)),xor:e=>i(t^BigInt(e)),equals:e=>t===BigInt(e),multiply:e=>i(t*BigInt(e)),shiftLeft:e=>i(t<<BigInt(e)),shiftRight:e=>i(t>>BigInt(e))}},o=e=>Array.from(Array(e).keys()),s=(...e)=>e.map(e=>-1===e?i(-1,-1):i(e)),a=s(31,0,1,2,3,4,-1,-1,3,4,5,6,7,8,-1,-1,7,8,9,10,11,12,-1,-1,11,12,13,14,15,16,-1,-1,15,16,17,18,19,20,-1,-1,19,20,21,22,23,24,-1,-1,23,24,25,26,27,28,-1,-1,27,28,29,30,31,30,-1,-1),c=s(57,49,41,33,25,17,9,1,59,51,43,35,27,19,11,3,61,53,45,37,29,21,13,5,63,55,47,39,31,23,15,7,56,48,40,32,24,16,8,0,58,50,42,34,26,18,10,2,60,52,44,36,28,20,12,4,62,54,46,38,30,22,14,6),u=s(39,7,47,15,55,23,63,31,38,6,46,14,54,22,62,30,37,5,45,13,53,21,61,29,36,4,44,12,52,20,60,28,35,3,43,11,51,19,59,27,34,2,42,10,50,18,58,26,33,1,41,9,49,17,57,25,32,0,40,8,48,16,56,24),l=[1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1],f=s(0,1048577,3145731),p=o(64).map(e=>Array(e).fill(null).reduce(e=>e.multiply(2),i(1)));p[p.length-1]=p[p.length-1].multiply(-1);let h=s(15,6,19,20,28,11,27,16,0,14,22,25,4,17,30,9,1,7,23,13,31,26,2,8,18,12,29,5,21,10,3,24),d=s(56,48,40,32,24,16,8,0,57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,60,52,44,36,28,20,12,4,27,19,11,3),y=s(13,16,10,23,0,4,-1,-1,2,27,14,5,20,9,-1,-1,22,18,11,3,25,7,-1,-1,15,6,26,19,12,1,-1,-1,40,51,30,36,46,54,-1,-1,29,39,50,44,32,47,-1,-1,43,48,38,55,33,52,-1,-1,45,41,49,35,28,31,-1,-1),g=[[14,4,3,15,2,13,5,3,13,14,6,9,11,2,0,5,4,1,10,12,15,6,9,10,1,8,12,7,8,11,7,0,0,15,10,5,14,4,9,10,7,8,12,3,13,1,3,6,15,12,6,11,2,9,5,0,4,2,11,14,1,7,8,13],[15,0,9,5,6,10,12,9,8,7,2,12,3,13,5,2,1,14,7,8,11,4,0,3,14,11,13,6,4,1,10,15,3,13,12,11,15,3,6,0,4,10,1,7,8,4,11,14,13,8,0,6,2,15,9,5,7,1,10,12,14,2,5,9],[10,13,1,11,6,8,11,5,9,4,12,2,15,3,2,14,0,6,13,1,3,15,4,10,14,9,7,12,5,0,8,7,13,1,2,4,3,6,12,11,0,13,5,14,6,8,15,2,7,10,8,15,4,9,11,5,9,0,14,3,10,7,1,12],[7,10,1,15,0,12,11,5,14,9,8,3,9,7,4,8,13,6,2,1,6,11,12,2,3,0,5,14,10,13,15,4,13,3,4,9,6,10,1,12,11,0,2,5,0,13,14,2,8,15,7,4,15,1,10,7,5,6,12,11,3,8,9,14],[2,4,8,15,7,10,13,6,4,1,3,12,11,7,14,0,12,2,5,9,10,13,0,3,1,11,15,5,6,8,9,14,14,11,5,6,4,1,3,10,2,12,15,0,13,2,8,5,11,8,0,15,7,14,9,4,12,7,10,9,1,13,6,3],[12,9,0,7,9,2,14,1,10,15,3,4,6,12,5,11,1,14,13,0,2,8,7,13,15,5,4,10,8,3,11,6,10,4,6,11,7,9,0,6,4,2,13,1,9,15,3,8,15,3,1,14,12,5,11,0,2,12,14,7,5,10,8,13],[4,1,3,10,15,12,5,0,2,11,9,6,8,7,6,9,11,4,12,15,0,3,10,5,14,13,7,8,13,14,1,2,13,6,14,9,4,1,2,14,11,13,5,0,1,10,8,3,0,11,3,5,9,4,15,2,7,8,12,15,10,7,6,12],[13,7,10,0,6,9,5,15,8,4,3,10,11,14,12,5,2,11,9,6,15,12,0,3,4,1,14,13,1,2,7,8,1,2,12,15,10,4,0,3,13,14,6,9,7,8,9,6,15,1,5,12,3,10,14,5,8,7,11,0,4,13,2,11]],m=(e,t,r)=>{let s=i(0);return o(t).forEach(t=>{e[t].isNegative()||r.and(p[e[t].low]).equals(0)||(s=s.or(p[t]))}),s},b=(e,t)=>{let r=o(8).map(()=>i(0)),s=[i(0),i(0)],l=i(0),f=i(0),p=m(c,64,t);return s[0]=p.and(0xffffffff),s[1]=p.and(-0x100000000).shiftRight(32),o(16).forEach(t=>{let c=i(0);f=(f=m(a,64,f=i(s[1]))).xor(e[t]),o(8).forEach(e=>{r[e]=f.shiftRight(8*e).and(255)}),o(8).reverse().forEach(e=>{c=c.shiftLeft(4).or(g[e][r[e]])}),f=m(h,32,c),l=i(s[0]),s[0]=i(s[1]),s[1]=l.xor(f)}),s.reverse(),p=m(u,64,p=s[1].shiftLeft(32).and(-0x100000000).or(s[0].and(0xffffffff)))},v=(e,t,r)=>{let s=i(0);o(8).forEach(e=>{s=i(t[e]).shiftLeft(8*e).or(s)});let a=Math.floor(e.length/8),c=o(16).map(()=>i(0));((e,t,r)=>{let i=m(d,56,e);o(16).forEach(e=>{i=i.and(f[l[e]]).shiftLeft(28-l[e]).or(i.and(f[l[e]].not()).shiftRight(l[e])),t[e]=m(y,64,i)}),1===r&&o(8).forEach(e=>{[t[e],t[15-e]]=[t[15-e],t[e]]})})(s,c,r);let u=o(a).map(()=>i(0));o(a).forEach(t=>{o(8).forEach(r=>{u[t]=i(e[r+8*t]).shiftLeft(8*r).or(u[t])})});let p=o(Math.floor((1+8*(a+1))/8)).map(()=>i(0));o(a).forEach(e=>{p[e]=b(c,u[e])});let h=e.slice(8*a),g=i(0);o(e.length%8).forEach(e=>{g=i(h[e]).shiftLeft(8*e).or(g)}),(h.length||0===r)&&(p[a]=b(c,g));let v=o(8*p.length).map(()=>0),w=0;return p.forEach(e=>{o(8).forEach(t=>{v[w]=e.shiftRight(8*t).and(255).low,w+=1})}),Buffer.from(v)},w=Buffer.from("ylzsxkwm"),_=e=>v(e,w,0);e.exports={encrypt:_,decrypt:e=>v(e,w,1),encryptQuery:e=>_(Buffer.from(e)).toString("base64")}},7979:(e,t,r)=>{"use strict";var i=r(8551);e.exports=function(){var e=i(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},7983:(e,t,r)=>{e.exports=r(9023).deprecate},8014:(e,t,r)=>{"use strict";var i=r(1291),o=Math.min;e.exports=function(e){var t=i(e);return t>0?o(t,0x1fffffffffffff):0}},8103:(e,t,r)=>{"use strict";let i=r(1753);e.exports=function({secret:e,serialize:t,wcLen:r,strict:o,isCensorFct:s,censorFctTakesPath:a},c){var u,l,f,p,h,d,y,g;let m=Function("o",`
    if (typeof o !== 'object' || o == null) {
      ${(u=o,l=t,!0===u?"throw Error('fast-redact: primitives cannot be redacted')":!1===l?"return o":"return this.serialize(o)")}
    }
    const { censor, secret } = this
    const originalSecret = {}
    const secretKeys = Object.keys(secret)
    for (var i = 0; i < secretKeys.length; i++) {
      originalSecret[secretKeys[i]] = secret[secretKeys[i]]
    }

    ${(f=e,p=s,h=a,Object.keys(f).map(e=>{let{escPath:t,leadingBracket:r,path:o}=f[e],s=+!!r,a=r?"":".",c=[];for(;null!==(u=i.exec(e));){let[,e]=u,{index:t,input:r}=u;t>s&&c.push(r.substring(0,t-!e))}var u,l=c.map(e=>`o${a}${e}`).join(" && ");0===l.length?l+=`o${a}${e} != null`:l+=` && o${a}${e} != null`;let d=`
      switch (true) {
        ${c.reverse().map(e=>`
          case o${a}${e} === censor:
            secret[${t}].circle = ${JSON.stringify(e)}
            break
        `).join(`
`)}
      }
    `,y=h?`val, ${JSON.stringify(o)}`:"val";return`
      if (${l}) {
        const val = o${a}${e}
        if (val === censor) {
          secret[${t}].precensored = true
        } else {
          secret[${t}].val = val
          o${a}${e} = ${p?`censor(${y})`:"censor"}
          ${d}
        }
      }
    `}).join(`
`))}
    this.compileRestore()
    ${(d=r>0,y=s,g=a,!0===d?`
    {
      const { wildcards, wcLen, groupRedact, nestedRedact } = this
      for (var i = 0; i < wcLen; i++) {
        const { before, beforeStr, after, nested } = wildcards[i]
        if (nested === true) {
          secret[beforeStr] = secret[beforeStr] || []
          nestedRedact(secret[beforeStr], o, before, after, censor, ${y}, ${g})
        } else secret[beforeStr] = groupRedact(o, before, censor, ${y}, ${g})
      }
    }
  `:"")}
    this.secret = originalSecret
    ${!1===t?"return o":`
    var s = this.serialize(o)
    this.restore(o)
    return s
  `}
  `).bind(c);return m.state=c,!1===t&&(m.restore=e=>c.restore(e)),m}},8111:(e,t,r)=>{"use strict";var i=r(6518),o=r(4576),s=r(679),a=r(8551),c=r(4901),u=r(2787),l=r(2106),f=r(4659),p=r(9039),h=r(9297),d=r(8227),y=r(7657).IteratorPrototype,g=r(3724),m=r(6395),b="constructor",v="Iterator",w=d("toStringTag"),_=TypeError,S=o[v],x=m||!c(S)||S.prototype!==y||!p(function(){S({})}),E=function(){if(s(this,y),u(this)===y)throw new _("Abstract class Iterator not directly constructable")},O=function(e,t){g?l(y,e,{configurable:!0,get:function(){return t},set:function(t){if(a(this),this===y)throw new _("You can't redefine this property");h(this,e)?this[e]=t:f(this,e,t)}}):y[e]=t};h(y,w)||O(w,v),(x||!h(y,b)||y[b]===Object)&&O(b,E),E.prototype=y,i({global:!0,constructor:!0,forced:x},{Iterator:E})},8114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(7333),r(4520),r(8992),r(5509),r(5223),r(321),r(1927),r(1632),r(4377),r(6771),r(2516),r(8931),r(2514),r(5694),r(2774),r(9536),r(1926),r(4483),r(6215),r(8159),r(7550),r(9920),r(3949),r(8335),r(6280),r(4905),r(8872);let i=r(7016).parse,o=r(1600),s=r(9664),a=r(478),c=r(3480),{isHost:u,cookieToMap:l,mapToCookie:f}=r(5853),{getManagedCacheStorage:p}=r(3971),{logScope:h}=r(5469),d=h("hook"),y=p("hook");y.aliveDuration=6048e5;let g=["true","cvip","svip"].includes((process.env.ENABLE_LOCAL_VIP||"").toLowerCase()),m="true"===(process.env.BLOCK_ADS||"").toLowerCase(),b="true"===(process.env.DISABLE_UPGRADE_CHECK||"").toLowerCase(),v="svip"===(process.env.ENABLE_LOCAL_VIP||"").toLowerCase(),w=(process.env.LOCAL_VIP_UID||"").split(",").map(e=>parseInt(e)).filter(e=>!Number.isNaN(e)),_={request:{before:()=>{},after:()=>{}},connect:{before:()=>{}},negotiate:{before:()=>{}},target:{host:new Set,path:new Set}};_.target.host=new Set(["music.163.com","interface.music.163.com","interface3.music.163.com","apm.music.163.com","apm3.music.163.com","interface.music.163.com.163jiasu.com","interface3.music.163.com.163jiasu.com"]),_.target.path=new Set(["/api/v3/playlist/detail","/api/v3/song/detail","/api/v6/playlist/detail","/api/album/play","/api/artist/privilege","/api/album/privilege","/api/v1/artist","/api/v1/artist/songs","/api/v2/artist/songs","/api/artist/top/song","/api/v1/album","/api/album/v3/detail","/api/playlist/privilege","/api/song/enhance/player/url","/api/song/enhance/player/url/v1","/api/song/enhance/download/url","/api/song/enhance/download/url/v1","/api/song/enhance/privilege","/api/ad","/batch","/api/batch","/api/listen/together/privilege/get","/api/playmode/intelligence/list","/api/v1/search/get","/api/v1/search/song/get","/api/search/complex/get","/api/search/complex/page","/api/search/pc/complex/get","/api/search/pc/complex/page","/api/search/song/list/page","/api/search/song/page","/api/cloudsearch/pc","/api/v1/playlist/manipulate/tracks","/api/song/like","/api/v1/play/record","/api/playlist/v4/detail","/api/v1/radio/get","/api/v1/discovery/recommend/songs","/api/usertool/sound/mobile/promote","/api/usertool/sound/mobile/theme","/api/usertool/sound/mobile/animationList","/api/usertool/sound/mobile/all","/api/usertool/sound/mobile/detail","/api/vipauth/app/auth/query","/api/music-vip-membership/client/vip/info"]);let S=["music.163.com","music.126.net","iplay.163.com","look.163.com","y.163.com","interface.music.163.com","interface3.music.163.com"];_.request.before=e=>{let{req:t}=e;t.url=(t.url.startsWith("http://")?"":(t.socket.encrypted?"https:":"http:")+"//"+(S.some(e=>(t.headers.host||"").includes(e))?t.headers.host:null))+t.url;let r=i(t.url);if([r.hostname,t.headers.host].some(e=>u(e,"music.163.com"))&&(e.decision="proxy"),process.env.NETEASE_COOKIE&&r.path.includes("url")){var a=l(t.headers.cookie);Object.entries(l(process.env.NETEASE_COOKIE)).forEach(([e,t])=>{a[e]=t}),t.headers.cookie=f(a),d.debug("Replace netease cookie")}if([r.hostname,t.headers.host].some(e=>_.target.host.has(e))&&"POST"===t.method&&(r.path.startsWith("/eapi/")||r.path.startsWith("/api/linux/forward")))return s.read(t).then(e=>t.body=e).then(s=>{if("x-napm-retry"in t.headers&&delete t.headers["x-napm-retry"],t.headers["X-Real-IP"]="************",!(t.url.includes("stream")||t.url.includes("/eapi/cloud/upload/check"))&&(t.headers["Accept-Encoding"]="gzip, deflate",s)){let t,a={};switch(a.pad=(s.match(/%0+$/)||[""])[0],"/api/linux/forward"===r.path?a.crypto="linuxapi":r.path.startsWith("/eapi/")?a.crypto="eapi":r.path.startsWith("/api/")&&(a.crypto="api"),a.crypto){case"linuxapi":t=JSON.parse(o.linuxapi.decrypt(Buffer.from(s.slice(8,s.length-a.pad.length),"hex")).toString()),a.path=i(t.url).path,a.param=t.params;break;case"eapi":t=o.eapi.decrypt(Buffer.from(s.slice(7,s.length-a.pad.length),"hex")).toString().split("-36cd479b6b5-"),a.path=t[0],a.param=JSON.parse(t[1]),a.param.hasOwnProperty("e_r")&&("true"==a.param.e_r||!0==a.param.e_r)?a.e_r=!0:a.e_r=!1;break;case"api":t={},decodeURIComponent(s).split("&").forEach(e=>{let[r,i]=e.split("=");t[r]=i}),a.path=r.path,a.param=t}if(a.path=a.path.replace(/\/\d*$/,""),e.netease=a,"/api/song/enhance/download/url"===a.path)return x(e);if("/api/song/enhance/download/url/v1"===a.path)return E(e);m&&a.path.startsWith("/api/ad")&&(e.error=Error("ADs blocked."),e.decision="close"),b&&a.path.match(/^\/api(\/v1)?\/(android|ios|osx|pc)\/(upgrade|version)/)&&(e.error=Error("Upgrade check blocked."),e.decision="close")}}).catch(e=>e&&d.error(e,`A error occurred in hook.request.before when hooking ${t.url}.`));if(_.target.host.has(r.hostname)&&(r.path.startsWith("/weapi/")||r.path.startsWith("/api/")))t.headers["X-Real-IP"]="************",e.netease={web:!0,path:r.path.replace(/^\/weapi\//,"/api/").split("?").shift().replace(/\/\d*$/,"")};else if(t.url.includes("package"))try{let r=t.url.split("package/").pop().split("/"),s=i(o.base64.decode(r[0])),a=r[1].replace(/\.\w+/,"");t.url=s.href,t.headers.host=s.hostname,t.headers.cookie=null,e.package={id:a},e.decision="proxy"}catch(t){e.error=t,e.decision="close"}},_.request.after=e=>{let{req:t,proxyRes:r,netease:a,package:c}=e;if("tyst.migu.cn"===t.headers.host&&r.headers["content-range"]&&200===r.statusCode&&(r.statusCode=206),a&&_.target.path.has(a.path)&&200===r.statusCode)return s.read(r,!0).then(e=>e.length?r.body=e:Promise.reject()).then(t=>{let r=e=>e.replace(/([^\\]"\s*:\s*)(\d{16,})(\s*[}|,])/g,'$1"$2L"$3');if(a.e_r?a.jsonBody=JSON.parse(r(o.eapi.decrypt(t).toString())):a.jsonBody=JSON.parse(r(t.toString())),g){let e="/api/music-vip-membership/client/vip/info";if("/batch"===a.path||"/api/batch"===a.path||a.path===e){let t=a.path===e?a.jsonBody:a.jsonBody[e],r={iconUrl:null,dynamicIconUrl:null,isSign:!1,isSignIap:!1,isSignDeduct:!1,isSignIapDeduct:!1};if(t&&(0===w.length||w.includes(t.data.userId)))try{let i=(t.data.now||new Date().getTime())+316224e5;t.data.redVipLevel=7,t.data.redVipAnnualCount=1,t.data.musicPackage={...r,...t.data.musicPackage,vipCode:230,vipLevel:7,expireTime:i},t.data.associator={...r,...t.data.associator,vipCode:100,vipLevel:7,expireTime:i},v&&(t.data.redplus={...r,...t.data.redplus,vipCode:300,vipLevel:7,expireTime:i},t.data.albumVip={...r,...t.data.albumVip,vipCode:400,vipLevel:0,expireTime:i}),a.path===e?a.jsonBody=t:a.jsonBody[e]=t}catch(e){d.debug({err:e},"Unable to apply the local VIP.")}}}if(new Set([401,512]).has(a.jsonBody.code)&&!a.web){if(a.path.includes("manipulate"))return O(e);else if("/api/song/like"===a.path)return k(e)}else if(a.path.includes("url"))return j(e);else if(a.path.includes("/usertool/sound/"))return T(a.jsonBody);else if(a.path.includes("batch"))for(let e in a.jsonBody)e.includes("/usertool/sound/")&&T(a.jsonBody[e]);else if(a.path.includes("/vipauth/app/auth/query"))return R(a.jsonBody)}).then(()=>{["transfer-encoding","content-encoding","content-length"].filter(e=>e in r.headers).forEach(e=>delete r.headers[e]);let e=JSON.stringify(a.jsonBody,(e,t)=>("object"==typeof t&&null!=t&&("cp"in t&&(t.cp=1),"fee"in t&&(t.fee=0),"downloadMaxbr"in t&&0===t.downloadMaxbr&&(t.downloadMaxbr=32e4),"dl"in t&&"downloadMaxbr"in t&&t.dl<t.downloadMaxbr&&(t.dl=t.downloadMaxbr),"playMaxbr"in t&&0===t.playMaxbr&&(t.playMaxbr=32e4),"pl"in t&&"playMaxbr"in t&&t.pl<t.playMaxbr&&(t.pl=t.playMaxbr),"sp"in t&&"st"in t&&"subp"in t&&(t.sp=7,t.st=0,t.subp=1),"start"in t&&"end"in t&&"playable"in t&&"unplayableType"in t&&"unplayableUserIds"in t&&(t.start=0,t.end=0,t.playable=!0,t.unplayableType="unknown",t.unplayableUserIds=[]),"noCopyrightRcmd"in t&&(t.noCopyrightRcmd=null),"payed"in t&&0==t.payed&&(t.payed=1),"flLevel"in t&&"none"===t.flLevel&&(t.flLevel="exhigh"),"plLevel"in t&&"none"===t.plLevel&&(t.plLevel="exhigh"),"dlLevel"in t&&"none"===t.dlLevel&&(t.dlLevel="exhigh")),t));e=e.replace(/([^\\]"\s*:\s*)"(\d{16,})L"(\s*[}|,])/g,"$1$2$3"),r.body=a.e_r?o.eapi.encrypt(Buffer.from(e)):e}).catch(e=>e&&d.error(e,`A error occurred in hook.request.after when hooking ${t.url}.`));if(c)if(new Set([201,301,302,303,307,308]).has(r.statusCode))return s(t.method,i(t.url).resolve(r.headers.location),t.headers).then(t=>e.proxyRes=t);else/p\d+c*\.music\.126\.net/.test(t.url)&&(r.headers["content-type"]="audio/*")},_.connect.before=e=>{let{req:t}=e,r=i("https://"+t.url);[r.hostname,t.headers.host].some(e=>_.target.host.has(e))?80===parseInt(r.port)?(t.url=`${global.address||"localhost"}:${global.port[0]}`,t.local=!0):global.port[1]?(t.url=`${global.address||"localhost"}:${global.port[1]}`,t.local=!0):e.decision="blank":r.href.includes(global.endpoint)&&(e.decision="proxy")},_.negotiate.before=e=>{let{req:t,socket:r,decision:o}=e,s=i("https://"+t.url),a=_.target.host;t.local||o||a.has(r.sni)&&!a.has(s.hostname)&&(a.add(s.hostname),e.decision="blank")};let x=e=>{let t,{req:r,netease:i}=e,s="http://music.163.com/api/song/enhance/player/url",{id:a,br:c,e_r:u,header:l}=i.param;switch(i.crypto){case"linuxapi":i.param={ids:`["${a}"]`,br:c},t=o.linuxapi.encryptRequest(s,i.param);break;case"eapi":case"api":i.param={ids:`["${a}"]`,br:c,e_r:u,header:l},"eapi"==i.crypto?t=o.eapi.encryptRequest(s,i.param):"api"==i.crypto&&(t=o.api.encryptRequest(s,i.param))}r.url=t.url,r.body=t.body+i.pad},E=e=>{let t,{req:r,netease:i}=e,s="http://music.163.com/api/song/enhance/player/url/v1",{id:a,level:c,immerseType:u,e_r:l,header:f}=i.param;switch(i.crypto){case"linuxapi":i.param={ids:`["${a}"]`,level:c,encodeType:"flac",immerseType:u},t=o.linuxapi.encryptRequest(s,i.param);break;case"eapi":case"api":i.param={ids:`["${a}"]`,level:c,encodeType:"flac",immerseType:u,e_r:l,header:f},"eapi"==i.crypto?t=o.eapi.encryptRequest(s,i.param):"api"==i.crypto&&(t=o.api.encryptRequest(s,i.param))}r.url=t.url,r.body=t.body+i.pad},O=e=>{let{req:t,netease:r}=e,{trackIds:i,pid:o,op:a}=r.param,c=(Array.isArray(i)?i:JSON.parse(i))[0];return s("POST","http://music.163.com/api/playlist/manipulate/tracks",t.headers,`trackIds=[${c},${c}]&pid=${o}&op=${a}`).then(e=>e.json()).then(e=>{r.jsonBody=e}).catch(e=>e&&d.error(e))},k=e=>{let{req:t,netease:r}=e,{trackId:i}=r.param,o=0,a=0;return s("GET","http://music.163.com/api/v1/user/info",t.headers).then(e=>e.json()).then(e=>(a=e.userPoint.userId,s("GET",`http://music.163.com/api/user/playlist?uid=${a}&limit=1`,t.headers).then(e=>e.json()))).then(e=>(o=e.playlist[0].id,s("POST","http://music.163.com/api/playlist/manipulate/tracks",t.headers,`trackIds=[${i},${i}]&pid=${o}&op=add`).then(e=>e.json()))).then(e=>{new Set([200,502]).has(e.code)&&(r.jsonBody={code:200,playlistId:o})}).catch(e=>e&&d.error(e))},j=e=>{let t,{req:r,netease:i}=e,{jsonBody:u}=i,l=Number(process.env.MIN_BR)||0,f=0,p=e=>{if(e.flag=0,(200!==e.code||e.freeTrialInfo||e.br<l)&&(0===f||e.id===f))return a(e.id).then(t=>{let s="";try{let{header:e}=i.param;e="string"==typeof e?JSON.parse(e):e;let t=c.parse(r.headers.cookie.replace(/\s/g,""),";");s=e.os||t.os}catch(e){}return e.type=999e3===t.br?"flac":"mp3","pc"===s||"uwp"===s?e.url=global.endpoint?`${global.endpoint.replace("https://","http://")}/package/${o.base64.encode(t.url)}/${e.id}.${e.type}`:t.url:e.url=global.endpoint?`${global.endpoint}/package/${o.base64.encode(t.url)}/${e.id}.${e.type}`:t.url,e.md5=t.md5||o.md5.digest(t.url),e.br=t.br||128e3,e.size=t.size,e.code=200,e.freeTrialInfo=null,t}).then(t=>{if(!i.path.includes("download")||t.md5)return;let a={android:"0.0.0",osx:"0.0.0"},u={key:t.url.replace(/\?.*$/,"").replace(/(?<=kugou\.com\/)\w+\/\w+\//,"").replace(/(?<=kuwo\.cn\/)\w+\/\w+\/resource\//,""),url:t.url};try{let{header:t}=i.param;t="string"==typeof t?JSON.parse(t):t;let l=c.parse(r.headers.cookie.replace(/\s/g,""),";"),f=t.os||l.os,p=t.appver||l.appver;if(f in a&&((e,t)=>{let r=Array.from([e,t]).map(e=>e.split(".").slice(0,3).map(e=>parseInt(e)||0)).reduce((e,t)=>e.length?e.map((e,r)=>e.concat(t[r])):t.map(e=>[e]),[]).filter(e=>e[0]!==e[1])[0];return!r||r[0]<=r[1]})(a[f],p))return y.cache(u,()=>s("GET",u.url).then(e=>o.md5.pipe(e))).then(t=>e.md5=t)}catch(e){}}).catch(e=>e&&d.error(e));200===e.code&&i.web&&(e.url=e.url.replace(/(m\d+?)(?!c)\.music\.126\.net/,"$1c.music.126.net"))};return Array.isArray(u.data)?i.path.includes("download")?(u.data=u.data[0],t=[p(u.data)]):(f=i.web?0:parseInt(((Array.isArray(i.param.ids)?i.param.ids:JSON.parse(i.param.ids))[0]||0).toString().replace("_0","")),t=u.data.map(e=>p(e))):t=[p(u.data)],Promise.all(t).catch(e=>e&&d.error(e))},T=e=>{d.debug("unblockSoundEffects() has been triggered.");let{data:t,code:r}=e;200===r&&(Array.isArray(t)?t.map(e=>{e.type&&(e.type=1)}):t.type&&(t.type=1))},R=e=>{d.debug("unblockLyricsEffects() has been triggered.");let{data:t,code:r}=e;200===r&&Array.isArray(t)&&t.forEach(e=>{"canUse"in e&&(e.canUse=!0),"canNotUseReasonCode"in e&&(e.canNotUseReasonCode=200)})};e.exports=_},8159:(e,t,r)=>{"use strict";var i=r(6518),o=r(6639).some;i({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{some:function(e){return o(this,e)}})},8167:e=>{"use strict";e.exports=require("worker_threads")},8227:(e,t,r)=>{"use strict";var i=r(4576),o=r(5745),s=r(9297),a=r(3392),c=r(4495),u=r(7040),l=i.Symbol,f=o("wks"),p=u?l.for||l:l&&l.withoutSetter||a;e.exports=function(e){return s(f,e)||(f[e]=c&&s(l,e)?l[e]:p("Symbol."+e)),f[e]}},8235:(e,t,r)=>{"use strict";var i=r(9504),o=r(9297),s=SyntaxError,a=parseInt,c=String.fromCharCode,u=i("".charAt),l=i("".slice),f=i(/./.exec),p={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":`
`,"\\r":"\r","\\t":"	"},h=/^[\da-f]{4}$/i,d=/^[\u0000-\u001F]$/;e.exports=function(e,t){for(var r=!0,i="";t<e.length;){var y=u(e,t);if("\\"===y){var g=l(e,t,t+2);if(o(p,g))i+=p[g],t+=2;else if("\\u"===g){var m=l(e,t+=2,t+4);if(!f(h,m))throw new s("Bad Unicode escape at: "+t);i+=c(a(m,16)),t+=4}else throw new s('Unknown escape sequence: "'+g+'"')}else if('"'===y){r=!1,t++;break}else{if(f(d,y))throw new s("Bad control character in string literal at: "+t);i+=y,t++}}if(r)throw new s("Unterminated string at: "+t);return{value:i,end:t}}},8237:(e,t,r)=>{"use strict";var i=r(6518),o=r(2652),s=r(9306),a=r(8551),c=r(1767),u=r(9539),l=r(4549),f=r(8745),p=r(9039),h=TypeError,d=p(function(){[].keys().reduce(function(){},void 0)}),y=!d&&l("reduce",h);i({target:"Iterator",proto:!0,real:!0,forced:d||y},{reduce:function(e){a(this);try{s(e)}catch(e){u(this,"throw",e)}var t=arguments.length<2,r=t?void 0:arguments[1];if(y)return f(y,this,t?[e]:[e,r]);var i=c(this),l=0;if(o(i,function(i){t?(t=!1,r=i):r=e(r,i,l),l++},{IS_RECORD:!0}),t)throw new h("Reduce of empty iterator with no initial value");return r}})},8330:e=>{"use strict";e.exports=JSON.parse('{"name":"@unblockneteasemusic/server","version":"0.27.10","description":"Revive unavailable songs for Netease Cloud Music","main":"src/provider/match.js","bin":{"unblockneteasemusic":"./precompiled/app.js"},"engines":{"node":">= 12"},"scripts":{"start:dev":"cross-env LOG_LEVEL=debug node src/app.js -e https://music.163.com","start":"cross-env node src/app.js -e https://music.163.com","build":"webpack","pkg":"pkg . --out-path=dist/","test":"jest"},"pkg":{"assets":["server.key","server.crt"],"targets":["node18-linux-arm64","node18-win-arm64","node18-linux-x64","node18-win-x64"],"outputPath":"dist"},"repository":{"type":"git","url":"https://github.com/UnblockNeteaseMusic/server.git"},"author":"nondanee, **********, pan93412","license":"LGPL-3.0-only","dependencies":{"node-windows":"^1.0.0-beta.8","pino":"6.14.0","pino-pretty":"^7.6.1"},"devDependencies":{"@swc/core":"^1.13.2","@types/node":"^24.1.0","@types/pino":"6.3.12","@yarnpkg/sdks":"^3.2.2","browserslist":"^4.25.1","core-js":"^3.44.0","cross-env":"^7.0.3","jest":"^30.0.5","pkg":"^5.8.1","prettier":"^3.6.2","swc-loader":"^0.2.6","terser-webpack-plugin":"^5.3.14","typescript":"^5.8.3","webpack":"^5.100.2","webpack-cli":"^6.0.1"},"resolutions":{"cacheable-request":"^10.2.9","minimatch":"^3.0.8","minimist":"^1.2.8","semver":"^7.5.4"},"publishConfig":{"access":"public"},"packageManager":"yarn@3.8.7"}')},8335:(e,t,r)=>{"use strict";var i=r(6518),o=r(3724),s=r(4576),a=r(7751),c=r(9504),u=r(9565),l=r(4901),f=r(34),p=r(4376),h=r(9297),d=r(655),y=r(6198),g=r(4659),m=r(9039),b=r(8235),v=r(4495),w=s.JSON,_=s.Number,S=s.SyntaxError,x=w&&w.parse,E=a("Object","keys"),O=Object.getOwnPropertyDescriptor,k=c("".charAt),j=c("".slice),T=c(/./.exec),R=c([].push),P=/^\d$/,L=/^[1-9]$/,M=/^[\d-]$/,A=/^[\t\n\r ]$/,N=function(e,t){var r=new B(e=d(e),0,""),i=r.parse(),o=i.value,s=r.skip(A,i.end);if(s<e.length)throw new S('Unexpected extra character: "'+k(e,s)+'" after the parsed data at: '+s);return l(t)?C({"":o},"",t,i):o},C=function(e,t,r,i){var o,s,a,c,l,d=e[t],g=i&&d===i.value,m=g&&"string"==typeof i.source?{source:i.source}:{};if(f(d)){var b=p(d),v=g?i.nodes:b?[]:{};if(b)for(c=0,o=v.length,a=y(d);c<a;c++)I(d,c,C(d,""+c,r,c<o?v[c]:void 0));else for(c=0,a=y(s=E(d));c<a;c++)I(d,l=s[c],C(d,l,r,h(v,l)?v[l]:void 0))}return u(r,e,t,d,m)},I=function(e,t,r){if(o){var i=O(e,t);if(i&&!i.configurable)return}void 0===r?delete e[t]:g(e,t,r)},D=function(e,t,r,i){this.value=e,this.end=t,this.source=r,this.nodes=i},B=function(e,t){this.source=e,this.index=t};B.prototype={fork:function(e){return new B(this.source,e)},parse:function(){var e=this.source,t=this.skip(A,this.index),r=this.fork(t),i=k(e,t);if(T(M,i))return r.number();switch(i){case"{":return r.object();case"[":return r.array();case'"':return r.string();case"t":return r.keyword(!0);case"f":return r.keyword(!1);case"n":return r.keyword(null)}throw new S('Unexpected character: "'+i+'" at: '+t)},node:function(e,t,r,i,o){return new D(t,i,e?null:j(this.source,r,i),o)},object:function(){for(var e=this.source,t=this.index+1,r=!1,i={},o={};t<e.length;){if("}"===k(e,t=this.until(['"',"}"],t))&&!r){t++;break}var s=this.fork(t).string(),a=s.value;t=s.end,t=this.until([":"],t)+1,t=this.skip(A,t),g(o,a,s=this.fork(t).parse()),g(i,a,s.value);var c=k(e,t=this.until([",","}"],s.end));if(","===c)r=!0,t++;else if("}"===c){t++;break}}return this.node(1,i,this.index,t,o)},array:function(){for(var e=this.source,t=this.index+1,r=!1,i=[],o=[];t<e.length;){if("]"===k(e,t=this.skip(A,t))&&!r){t++;break}var s=this.fork(t).parse();if(R(o,s),R(i,s.value),","===k(e,t=this.until([",","]"],s.end)))r=!0,t++;else if("]"===k(e,t)){t++;break}}return this.node(1,i,this.index,t,o)},string:function(){var e=this.index,t=b(this.source,this.index+1);return this.node(0,t.value,e,t.end)},number:function(){var e=this.source,t=this.index,r=t;if("-"===k(e,r)&&r++,"0"===k(e,r))r++;else if(T(L,k(e,r)))r=this.skip(P,r+1);else throw new S("Failed to parse number at: "+r);if("."===k(e,r)&&(r=this.skip(P,r+1)),("e"===k(e,r)||"E"===k(e,r))&&(("+"===k(e,++r)||"-"===k(e,r))&&r++,r===(r=this.skip(P,r))))throw new S("Failed to parse number's exponent value at: "+r);return this.node(0,_(j(e,t,r)),t,r)},keyword:function(e){var t=""+e,r=this.index,i=r+t.length;if(j(this.source,r,i)!==t)throw new S("Failed to parse value at: "+r);return this.node(0,e,r,i)},skip:function(e,t){for(var r=this.source;t<r.length&&T(e,k(r,t));t++);return t},until:function(e,t){t=this.skip(A,t);for(var r=k(this.source,t),i=0;i<e.length;i++)if(e[i]===r)return t;throw new S('Unexpected character: "'+r+'" at: '+t)}};var $=m(function(){var e,t="9007199254740993";return x(t,function(t,r,i){e=i.source}),e!==t}),q=v&&!m(function(){return 1/x("-0 	")!=-1/0});i({target:"JSON",stat:!0,forced:$},{parse:function(e,t){return q&&!l(t)?x(e):N(e,t)}})},8409:e=>{"use strict";e.exports={mapHttpRequest:function(e){return{req:i(e)}},reqSerializer:i};let t=Symbol("pino-raw-req-ref"),r=Object.create({},{id:{enumerable:!0,writable:!0,value:""},method:{enumerable:!0,writable:!0,value:""},url:{enumerable:!0,writable:!0,value:""},query:{enumerable:!0,writable:!0,value:""},params:{enumerable:!0,writable:!0,value:""},headers:{enumerable:!0,writable:!0,value:{}},remoteAddress:{enumerable:!0,writable:!0,value:""},remotePort:{enumerable:!0,writable:!0,value:""},raw:{enumerable:!1,get:function(){return this[t]},set:function(e){this[t]=e}}});function i(e){let t=e.info||e.socket,i=Object.create(r);return i.id="function"==typeof e.id?e.id():e.id||(e.info?e.info.id:void 0),i.method=e.method,e.originalUrl?(i.url=e.originalUrl,i.query=e.query,i.params=e.params):i.url=e.path||(e.url?e.url.path||e.url:void 0),i.headers=e.headers,i.remoteAddress=t&&t.remoteAddress,i.remotePort=t&&t.remotePort,i.raw=e.raw||e,i}Object.defineProperty(r,t,{writable:!0,value:{}})},8463:e=>{e.exports=a,a.default=a,a.stable=l,a.stableStringify=l;var t="[...]",r="[Circular]",i=[],o=[];function s(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function a(e,a,u,l){void 0===l&&(l=s()),function e(i,o,s,a,u,l,f){if(l+=1,"object"==typeof i&&null!==i){for(p=0;p<a.length;p++)if(a[p]===i)return void c(r,i,o,u);if(void 0!==f.depthLimit&&l>f.depthLimit||void 0!==f.edgesLimit&&s+1>f.edgesLimit)return void c(t,i,o,u);if(a.push(i),Array.isArray(i))for(p=0;p<i.length;p++)e(i[p],p,p,a,i,l,f);else{var p,h=Object.keys(i);for(p=0;p<h.length;p++){var d=h[p];e(i[d],d,p,a,i,l,f)}}a.pop()}}(e,"",0,[],void 0,0,l);try{p=0===o.length?JSON.stringify(e,a,u):JSON.stringify(e,f(a),u)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==i.length;){var p,h=i.pop();4===h.length?Object.defineProperty(h[0],h[1],h[3]):h[0][h[1]]=h[2]}}return p}function c(e,t,r,s){var a=Object.getOwnPropertyDescriptor(s,r);void 0!==a.get?a.configurable?(Object.defineProperty(s,r,{value:e}),i.push([s,r,t,a])):o.push([t,r,e]):(s[r]=e,i.push([s,r,t]))}function u(e,t){return e<t?-1:+(e>t)}function l(e,a,l,p){void 0===p&&(p=s());var h,d=function e(o,s,a,l,f,p,h){if(p+=1,"object"==typeof o&&null!==o){for(d=0;d<l.length;d++)if(l[d]===o)return void c(r,o,s,f);try{if("function"==typeof o.toJSON)return}catch(e){return}if(void 0!==h.depthLimit&&p>h.depthLimit||void 0!==h.edgesLimit&&a+1>h.edgesLimit)return void c(t,o,s,f);if(l.push(o),Array.isArray(o))for(d=0;d<o.length;d++)e(o[d],d,d,l,o,p,h);else{var d,y={},g=Object.keys(o).sort(u);for(d=0;d<g.length;d++){var m=g[d];e(o[m],m,d,l,o,p,h),y[m]=o[m]}if(void 0===f)return y;i.push([f,s,o]),f[s]=y}l.pop()}}(e,"",0,[],void 0,0,p)||e;try{h=0===o.length?JSON.stringify(d,a,l):JSON.stringify(d,f(a),l)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==i.length;){var y=i.pop();4===y.length?Object.defineProperty(y[0],y[1],y[3]):y[0][y[1]]=y[2]}}return h}function f(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(o.length>0)for(var i=0;i<o.length;i++){var s=o[i];if(s[1]===t&&s[0]===r){r=s[2],o.splice(i,1);break}}return e.call(this,t,r)}}},8469:(e,t,r)=>{"use strict";var i=r(9504),o=r(507),s=r(4402),a=s.Set,c=s.proto,u=i(c.forEach),l=i(c.keys),f=l(new a).next;e.exports=function(e,t,r){return r?o({iterator:l(e),next:f},t):u(e,t)}},8480:(e,t,r)=>{"use strict";var i=r(1828),o=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,o)}},8527:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402).has,s=r(5170),a=r(3789),c=r(507),u=r(9539);e.exports=function(e){var t=i(this),r=a(e);if(s(t)<r.size)return!1;var l=r.getIterator();return!1!==c(l,function(e){if(!o(t,e))return u(l,"normal",!1)})}},8551:(e,t,r)=>{"use strict";var i=r(34),o=String,s=TypeError;e.exports=function(e){if(i(e))return e;throw new s(o(e)+" is not an object")}},8611:e=>{"use strict";e.exports=require("http")},8622:(e,t,r)=>{"use strict";var i=r(4576),o=r(4901),s=i.WeakMap;e.exports=o(s)&&/native code/.test(String(s))},8624:(e,t,r)=>{"use strict";let i=r(1423),{redactFmtSym:o,wildcardFirstSym:s}=r(544),{rx:a,validator:c}=i,u=c({ERR_PATHS_MUST_BE_STRINGS:()=>"pino – redacted paths must be strings",ERR_INVALID_PATH:e=>`pino – redact paths array contains an invalid path (${e})`}),l="[Redacted]";e.exports=function(e,t){let{paths:r,censor:c}=function(e){if(Array.isArray(e))return u(e={paths:e,censor:l}),e;let{paths:t,censor:r=l,remove:i}=e;if(!1===Array.isArray(t))throw Error("pino – redact must contain an array of strings");return!0===i&&(r=void 0),u({paths:t,censor:r}),{paths:t,censor:r}}(e),f=r.reduce((e,t)=>{a.lastIndex=0;let r=a.exec(t),i=a.exec(t),o=void 0!==r[1]?r[1].replace(/^(?:"|'|`)(.*)(?:"|'|`)$/,"$1"):r[0];if("*"===o&&(o=s),null===i)return e[o]=null,e;if(null===e[o])return e;let{index:c}=i,u=`${t.substr(c,t.length-1)}`;return e[o]=e[o]||[],o!==s&&0===e[o].length&&e[o].push(...e[s]||[]),o===s&&Object.keys(e).forEach(function(t){e[t]&&e[t].push(u)}),e[o].push(u),e},{}),p={[o]:i({paths:r,censor:c,serialize:t,strict:!1})};return[...Object.keys(f),...Object.getOwnPropertySymbols(f)].reduce((e,r)=>{if(null===f[r])e[r]=e=>((...e)=>"function"==typeof c?t(c(...e)):t(c))(e,[r]);else{let o="function"==typeof c?(e,t)=>c(e,[r,...t]):c;e[r]=i({paths:f[r],censor:o,serialize:t,strict:!1})}return e},p)}},8686:(e,t,r)=>{"use strict";var i=r(3724),o=r(9039);e.exports=i&&o(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8687:(e,t,r)=>{"use strict";let{EventEmitter:i}=r(4434),o=r(809),s=r(61),a=r(9502),{lsCacheSym:c,levelValSym:u,setLevelSym:l,getLevelSym:f,chindingsSym:p,parsedChindingsSym:h,mixinSym:d,asJsonSym:y,writeSym:g,mixinMergeStrategySym:m,timeSym:b,timeSliceIndexSym:v,streamSym:w,serializersSym:_,formattersSym:S,useOnlyCustomLevelsSym:x,needsMetadataGsym:E,redactFmtSym:O,stringifySym:k,formatOptsSym:j,stringifiersSym:T}=r(544),{getLevel:R,setLevel:P,isLevelEnabled:L,mappings:M,initialLsCache:A,genLsCache:N,assertNoLevelCollisions:C}=r(754),{asChindings:I,asJson:D,buildFormatters:B,stringify:$}=r(6526),{version:q}=r(5660),U=r(8624),F={constructor:class{},child:function(e,t){if(!e)throw Error("missing bindings for child Pino");t=t||{};let r=this[_],i=this[S],o=Object.create(this);if(!0===e.hasOwnProperty("serializers")&&(a.emit("PINODEP004"),t.serializers=e.serializers),!0===e.hasOwnProperty("formatters")&&(a.emit("PINODEP005"),t.formatters=e.formatters),!0===e.hasOwnProperty("customLevels")&&(a.emit("PINODEP006"),t.customLevels=e.customLevels),!0===e.hasOwnProperty("level")&&(a.emit("PINODEP007"),t.level=e.level),!0===t.hasOwnProperty("serializers")){for(let e in o[_]=Object.create(null),r)o[_][e]=r[e];let e=Object.getOwnPropertySymbols(r);for(var s=0;s<e.length;s++){let t=e[s];o[_][t]=r[t]}for(let e in t.serializers)o[_][e]=t.serializers[e];let i=Object.getOwnPropertySymbols(t.serializers);for(var c=0;c<i.length;c++){let e=i[c];o[_][e]=t.serializers[e]}}else o[_]=r;if(t.hasOwnProperty("formatters")){let{level:e,bindings:r,log:s}=t.formatters;o[S]=B(e||i.level,r||W,s||i.log)}else o[S]=B(i.level,W,i.log);if(!0===t.hasOwnProperty("customLevels")&&(C(this.levels,t.customLevels),o.levels=M(t.customLevels,o[x]),N(o)),"object"==typeof t.redact&&null!==t.redact||Array.isArray(t.redact)){o.redact=t.redact;let e=U(o.redact,$),r={stringify:e[O]};o[k]=$,o[T]=e,o[j]=r}o[p]=I(o,e);let u=t.level||this.level;return o[l](u),o},bindings:function(){let e=this[p],t=JSON.parse(`{${e.substr(1)}}`);return delete t.pid,delete t.hostname,t},setBindings:function(e){let t=I(this,e);this[p]=t,delete this[h]},flush:function(){let e=this[w];"flush"in e&&e.flush()},isLevelEnabled:L,version:q,get level(){return this[f]()},set level(lvl){this[l](lvl)},get levelVal(){return this[u]},set levelVal(n){throw Error("levelVal is read-only")},[c]:A,[g]:function(e,t,r){let i,a=this[b](),c=this[d],u=this[m]||G,l=e instanceof Error;null==e?i=c?c({}):{}:(i=u(e,c?c(e):{}),!t&&l&&(t=e.message),l&&(i.stack=e.stack,i.type||(i.type="Error")));let f=this[y](i,t,r,a),p=this[w];!0===p[E]&&(p.lastLevel=r,p.lastObj=i,p.lastMsg=t,p.lastTime=a.slice(this[v]),p.lastLogger=this),p instanceof o?p.write(f):p.write(s(f))},[y]:D,[f]:R,[l]:P};Object.setPrototypeOf(F,i.prototype),e.exports=function(){return Object.create(F)};let W=e=>e;function G(e,t){return Object.assign(t,e)}},8727:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(e,t,r)=>{"use strict";var i=r(616),o=Function.prototype,s=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(i?a.bind(s):function(){return a.apply(s,arguments)})},8750:(e,t,r)=>{"use strict";var i=r(7080),o=r(4402),s=r(5170),a=r(3789),c=r(8469),u=r(507),l=o.Set,f=o.add,p=o.has;e.exports=function(e){var t=i(this),r=a(e),o=new l;return s(t)>r.size?u(r.getIterator(),function(e){p(t,e)&&f(o,e)}):c(t,function(e){r.includes(e)&&f(o,e)}),o}},8773:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor;t.f=i&&!r.call({1:2},1)?function(e){var t=i(this,e);return!!t&&t.enumerable}:r},8803:(e,t,r)=>{"use strict";let i=r(4434),o="cancel";e.exports={CancelRequest:class extends i{cancelled=!1;cancel(){this.cancelled=!0,this.emit(o)}},ON_CANCEL:o}},8872:(e,t,r)=>{"use strict";r(8237)},8931:(e,t,r)=>{"use strict";var i=r(6518),o=r(9565),s=r(7650),a=r(3838);i({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){return o(a,this,s(e))}})},8981:(e,t,r)=>{"use strict";var i=r(7750),o=Object;e.exports=function(e){return o(i(e))}},8992:(e,t,r)=>{"use strict";r(8111)},9023:e=>{"use strict";e.exports=require("util")},9039:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},9278:e=>{"use strict";e.exports=require("net")},9286:(e,t,r)=>{"use strict";var i=r(4402),o=r(8469),s=i.Set,a=i.add;e.exports=function(e){var t=new s;return o(e,function(e){a(t,e)}),t}},9297:(e,t,r)=>{"use strict";var i=r(9504),o=r(8981),s=i({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return s(o(e),t)}},9306:(e,t,r)=>{"use strict";var i=r(4901),o=r(6823),s=TypeError;e.exports=function(e){if(i(e))return e;throw new s(o(e)+" is not a function")}},9418:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(6280),e.exports=class extends Error{constructor(e){super(`This request URL has been cancelled: ${e}`),this.name="RequestCancelled"}}},9433:(e,t,r)=>{"use strict";var i=r(4576),o=Object.defineProperty;e.exports=function(e,t){try{o(i,e,{value:t,configurable:!0,writable:!0})}catch(r){i[e]=t}return t}},9462:(e,t,r)=>{"use strict";var i=r(9565),o=r(2360),s=r(6699),a=r(6279),c=r(8227),u=r(1181),l=r(5966),f=r(7657).IteratorPrototype,p=r(2529),h=r(9539),d=r(1385),y=c("toStringTag"),g="IteratorHelper",m="WrapForValidIterator",b="normal",v="throw",w=u.set,_=function(e){var t=u.getterFor(e?m:g);return a(o(f),{next:function(){var r=t(this);if(e)return r.nextHandler();if(r.done)return p(void 0,!0);try{var i=r.nextHandler();return r.returnHandlerResult?i:p(i,r.done)}catch(e){throw r.done=!0,e}},return:function(){var r=t(this),o=r.iterator;if(r.done=!0,e){var s=l(o,"return");return s?i(s,o):p(void 0,!0)}if(r.inner)try{h(r.inner.iterator,b)}catch(e){return h(o,v,e)}if(r.openIters)try{d(r.openIters,b)}catch(e){return h(o,v,e)}return o&&h(o,b),p(void 0,!0)}})},S=_(!0),x=_(!1);s(x,y,"Iterator Helper"),e.exports=function(e,t,r){var i=function(i,o){o?(o.iterator=i.iterator,o.next=i.next):o=i,o.type=t?m:g,o.returnHandlerResult=!!r,o.nextHandler=e,o.counter=0,o.done=!1,w(this,o)};return i.prototype=t?S:x,i}},9479:(e,t,r)=>{"use strict";var i=r(3724),o=r(2106),s=r(5213),a=r(7979);i&&!s.correct&&(o(RegExp.prototype,"flags",{configurable:!0,get:a}),s.correct=!0)},9502:(e,t,r)=>{"use strict";let i=r(6187)();e.exports=i;let o="PinoWarning";i.create(o,"PINODEP004","bindings.serializers is deprecated, use options.serializers option instead"),i.create(o,"PINODEP005","bindings.formatters is deprecated, use options.formatters option instead"),i.create(o,"PINODEP006","bindings.customLevels is deprecated, use options.customLevels option instead"),i.create(o,"PINODEP007","bindings.level is deprecated, use options.level option instead")},9504:(e,t,r)=>{"use strict";var i=r(616),o=Function.prototype,s=o.call,a=i&&o.bind.bind(s,s);e.exports=i?a:function(e){return function(){return s.apply(e,arguments)}}},9519:(e,t,r)=>{"use strict";var i,o,s=r(4576),a=r(2839),c=s.process,u=s.Deno,l=c&&c.versions||u&&u.version,f=l&&l.v8;f&&(o=(i=f.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!o&&a&&(!(i=a.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=a.match(/Chrome\/(\d+)/))&&(o=+i[1]),e.exports=o},9536:(e,t,r)=>{"use strict";var i=r(6518),o=r(9306),s=r(7080),a=r(8469),c=TypeError;i({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=s(this),r=arguments.length<2,i=r?void 0:arguments[1];if(o(e),a(t,function(o){r?(r=!1,i=o):i=e(i,o,o,t)}),r)throw new c("Reduce of empty set with no initial value");return i}})},9539:(e,t,r)=>{"use strict";var i=r(9565),o=r(8551),s=r(5966);e.exports=function(e,t,r){var a,c;o(e);try{if(!(a=s(e,"return"))){if("throw"===t)throw r;return r}a=i(a,e)}catch(e){c=!0,a=e}if("throw"===t)throw r;if(c)throw a;return o(a),r}},9565:(e,t,r)=>{"use strict";var i=r(616),o=Function.prototype.call;e.exports=i?o.bind(o):function(){return o.apply(o,arguments)}},9617:(e,t,r)=>{"use strict";var i=r(5397),o=r(5610),s=r(6198),a=function(e){return function(t,r,a){var c,u=i(t),l=s(u);if(0===l)return!e&&-1;var f=o(a,l);if(e&&r!=r){for(;l>f;)if((c=u[f++])!=c)return!0}else for(;l>f;f++)if((e||f in u)&&u[f]===r)return e||f||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},9643:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(3064),r(2577),r(8992),e.exports=(e,t)=>{let{duration:r}=t,i=e.slice(0,5).find(e=>e.duration&&5e3>Math.abs(e.duration-r));return i||e[0]},e.exports.ENABLE_FLAC="true"===(process.env.ENABLE_FLAC||"").toLowerCase()},9664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(4905),r(8872),r(8992),r(4114),r(8335);let i=r(3106),o=r(8611),s=r(5692),a=r(8803),c=r(9418),{logScope:u}=r(5469),l=r(7016).parse,f=r(7016).format,p=u("request"),h=e=>(global.hosts||{})[e]||e,d=(e,t)=>("https:"===((void 0===t?global.proxy:t)||e).protocol?s:o).request,y=(e,t,r,i)=>{r=r||{},i=void 0===i?global.proxy:i,"content-length"in r&&delete r["content-length"];let o={};return o._headers=r,i&&"https:"===t.protocol?(o.method="CONNECT",o.headers=Object.keys(r).reduce((e,t)=>Object.assign(e,["host","user-agent"].includes(t)&&{[t]:r[t]}),{})):(o.method=e,o.headers=r),i?(o.hostname=h(i.hostname),o.port=i.port||("https:"===i.protocol?443:80),o.path="https:"===t.protocol?h(t.hostname)+":"+(t.port||443):"http://"+h(t.hostname)+t.path):(o.hostname=h(t.hostname),o.port=t.port||("https:"===t.protocol?443:80),o.path=t.path),o},g=(e,t,r,i,o,u)=>{let h=l(t),w=r||{},_=y(e,h,{host:h.hostname,accept:"application/json, text/plain, */*","accept-encoding":"gzip, deflate","accept-language":"zh-CN,zh;q=0.9","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",...w},o);return new Promise((r,l)=>{var y;p.debug(`Start requesting ${t}`);let g=d(h,o)(_),m=function(){g.destroy(new c(f(h)))};null==u||u.on(a,m),null!=(y=null==u?void 0:u.cancelled)&&y&&m(),g.setTimeout(1e4,()=>{p.warn({url:f(h)},"The request timed out, or the requester didn't handle the response."),m()}).on("response",e=>r(e)).on("connect",(t,o)=>{p.debug("received CONNECT, continuing with https.request()..."),s.request({method:e,path:h.path,headers:_._headers,socket:o,agent:!1}).on("response",e=>r(e)).on("error",e=>l(e)).end(i)}).on("error",e=>l(e)).end("CONNECT"===_.method.toUpperCase()?void 0:i)}).then(t=>{var r;if(null!=(r=null==u?void 0:u.cancelled)&&r)return Promise.reject(new c(f(h)));if([201,301,302,303,307,308].includes(t.statusCode)){let r=h.resolve(t.headers.location||h.href);return p.debug(`Redirect to ${r}`),delete w.host,g(e,r,w,i,o)}return Object.assign(t,{url:h,body:e=>m(t,e),json:()=>b(t),jsonp:()=>v(t)})})},m=(e,t)=>new Promise((t,r)=>{let i=[];e.on("data",e=>i.push(e)).on("end",()=>t(Buffer.concat(i))).on("error",e=>r(e))}).then(r=>{if(r.length)switch(e.headers["content-encoding"]){case"deflate":case"gzip":r=i.unzipSync(r);break;case"br":r=i.brotliDecompressSync(r)}return t?r:r.toString()}),b=e=>m(e,!1).then(e=>JSON.parse(e)),v=e=>m(e,!1).then(e=>JSON.parse(e.slice(e.indexOf("(")+1,-1)));g.read=m,g.create=d,g.translate=h,g.configure=y,e.exports=g},9717:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454),r(3064),r(2577),r(8992);let i=r(2569),o=r(9643),s=r(9664),{getManagedCacheStorage:a}=r(3971),c=process.env.NEW_QQ_COOKIE||process.env.QQ_COOKIE,u={origin:"http://y.qq.com/",referer:"http://y.qq.com/",cookie:c||null},l=e=>({id:{song:e.mid,file:e.mid},name:e.name,duration:1e3*e.interval,album:{id:e.album.mid,name:e.album.name},artists:e.singer.map(({mid:e,name:t})=>({id:e,name:t}))}),f=e=>(e.key=e.file,Promise.all([["F000",".flac"],["M800",".mp3"],["M500",".mp3"],[null,null]].slice(u.cookie||"undefined"!=typeof window?+!o.ENABLE_FLAC:2).map(t=>((e,t)=>{let r=((u.cookie||"").match(/uin=(\d+)/)||[])[1]||"0";return s("GET","https://u.y.qq.com/cgi-bin/musicu.fcg?data="+encodeURIComponent(JSON.stringify({req_0:{module:"vkey.GetVkeyServer",method:"CgiGetVkey",param:{guid:(1e7*Math.random()).toFixed(0),loginflag:1,filename:t[0]?[t.join(e.file)]:null,songmid:[e.song],songtype:[0],uin:r,platform:"20"}}})),u).then(e=>e.json()).then(e=>{let{sip:t,midurlinfo:r}=e.req_0.data;if(!r[0].purl)return Promise.reject();let i=t[0]+r[0].purl;return s("GET",i,{range:"bytes=0-8191","accept-encoding":"identity"}).then(e=>e.statusCode<200||e.statusCode>299?Promise.reject():i)})})(e,t).catch(()=>null))).then(e=>e.find(e=>e)||Promise.reject()).catch(()=>i().qq.track(e))),p=a("provider/qq");e.exports={check:e=>{let t=Math.floor(Date.now()/1e3),r=((c||"").match(/musickey_createtime=(\d+)/)||[])[1]||"",i=parseInt(r,10)+259200;return r&&i-t<600&&(()=>{let e=(c.match(/qqrefresh_token=([^;]*)/)||[])[1]||"",t=(c.match(/qqmusic_key=([^;]*)/)||[])[1]||"",r=parseInt((c.match(/uin=(\d+)/)||[])[1]||0,10);return s("POST","https://u.y.qq.com/cgi-bin/musicu.fcg",{Cookie:c,"Content-Type":"application/json"},JSON.stringify({req:{module:"music.login.LoginServer",method:"Login",param:{refresh_token:e,musickey:t,musicid:r}}})).then(e=>e.json()).then(e=>{var t;let r=(null==(t=e.req)?void 0:t.data)||{},i=r.musickey,o=r.musickeyCreateTime;if(!i||!o)return;let s=c;s=s.replace(/(qqmusic_key=)[^;]*/,`$1${i}`).replace(/(qm_keyst=)[^;]*/,`$1${i}`).replace(/(psrf_musickey_createtime=)[^;]*/,`$1${o}`),process.env.NEW_QQ_COOKIE=s,u.cookie=s,c=s})})(),p.cache(e,()=>(e=>s("GET","https://u.y.qq.com/cgi-bin/musicu.fcg?data="+encodeURIComponent(JSON.stringify({search:{method:"DoSearchForQQMusicDesktop",module:"music.search.SearchCgiService",param:{num_per_page:5,page_num:1,query:e.keyword,search_type:0}}})),u).then(e=>e.json()).then(t=>{let r=o(t.search.data.body.song.list.map(l),e);return r?r.id:Promise.reject()}))(e)).then(f)},track:f}},9733:(e,t,r)=>{"use strict";var i=r(6518),o=r(9306),s=r(6194),a=r(2248),c=TypeError,u=a.get,l=a.has,f=a.set;i({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=s(this),i=arguments.length;o(t);var a=l(r,e);if(!a&&i<3)throw new c("Updating absent value");var p=a?u(r,e):o(i>2?arguments[2]:void 0)(e,r);return f(r,e,t(p,e,r)),r}})},9844:e=>{"use strict";function t(e){return e instanceof Buffer?Buffer.from(e):new e.constructor(e.buffer.slice(),e.byteOffset,e.length)}e.exports=function(e){if((e=e||{}).circles)return function(e){let r=[],i=[],o=new Map;if(o.set(Date,e=>new Date(e)),o.set(Map,(e,t)=>new Map(a(Array.from(e),t))),o.set(Set,(e,t)=>new Set(a(Array.from(e),t))),e.constructorHandlers)for(let t of e.constructorHandlers)o.set(t[0],t[1]);let s=null;return e.proto?function e(c){if("object"!=typeof c||null===c)return c;if(Array.isArray(c))return a(c,e);if(c.constructor!==Object&&(s=o.get(c.constructor)))return s(c,e);let u={};for(let a in r.push(c),i.push(u),c){let l=c[a];if("object"!=typeof l||null===l)u[a]=l;else if(l.constructor!==Object&&(s=o.get(l.constructor)))u[a]=s(l,e);else if(ArrayBuffer.isView(l))u[a]=t(l);else{let t=r.indexOf(l);-1!==t?u[a]=i[t]:u[a]=e(l)}}return r.pop(),i.pop(),u}:function e(c){if("object"!=typeof c||null===c)return c;if(Array.isArray(c))return a(c,e);if(c.constructor!==Object&&(s=o.get(c.constructor)))return s(c,e);let u={};for(let a in r.push(c),i.push(u),c){if(!1===Object.hasOwnProperty.call(c,a))continue;let l=c[a];if("object"!=typeof l||null===l)u[a]=l;else if(l.constructor!==Object&&(s=o.get(l.constructor)))u[a]=s(l,e);else if(ArrayBuffer.isView(l))u[a]=t(l);else{let t=r.indexOf(l);-1!==t?u[a]=i[t]:u[a]=e(l)}}return r.pop(),i.pop(),u};function a(e,a){let c=Object.keys(e),u=Array(c.length);for(let l=0;l<c.length;l++){let f=c[l],p=e[f];if("object"!=typeof p||null===p)u[f]=p;else if(p.constructor!==Object&&(s=o.get(p.constructor)))u[f]=s(p,a);else if(ArrayBuffer.isView(p))u[f]=t(p);else{let e=r.indexOf(p);-1!==e?u[f]=i[e]:u[f]=a(p)}}return u}}(e);let r=new Map;if(r.set(Date,e=>new Date(e)),r.set(Map,(e,t)=>new Map(o(Array.from(e),t))),r.set(Set,(e,t)=>new Set(o(Array.from(e),t))),e.constructorHandlers)for(let t of e.constructorHandlers)r.set(t[0],t[1]);let i=null;return e.proto?function e(s){if("object"!=typeof s||null===s)return s;if(Array.isArray(s))return o(s,e);if(s.constructor!==Object&&(i=r.get(s.constructor)))return i(s,e);let a={};for(let o in s){let c=s[o];"object"!=typeof c||null===c?a[o]=c:c.constructor!==Object&&(i=r.get(c.constructor))?a[o]=i(c,e):ArrayBuffer.isView(c)?a[o]=t(c):a[o]=e(c)}return a}:function e(s){if("object"!=typeof s||null===s)return s;if(Array.isArray(s))return o(s,e);if(s.constructor!==Object&&(i=r.get(s.constructor)))return i(s,e);let a={};for(let o in s){if(!1===Object.hasOwnProperty.call(s,o))continue;let c=s[o];"object"!=typeof c||null===c?a[o]=c:c.constructor!==Object&&(i=r.get(c.constructor))?a[o]=i(c,e):ArrayBuffer.isView(c)?a[o]=t(c):a[o]=e(c)}return a};function o(e,o){let s=Object.keys(e),a=Array(s.length);for(let c=0;c<s.length;c++){let u=s[c],l=e[u];"object"!=typeof l||null===l?a[u]=l:l.constructor!==Object&&(i=r.get(l.constructor))?a[u]=i(l,o):ArrayBuffer.isView(l)?a[u]=t(l):a[u]=o(l)}return a}}},9873:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454);let i=r(2569),o=r(9643),s=r(1600),a=r(9664),{getManagedCacheStorage:c}=r(3971),u=e=>({id:e.MUSICRID.split("_").pop(),name:e.SONGNAME,duration:1e3*e.DURATION,album:{id:e.ALBUMID,name:e.ALBUM},artists:e.ARTIST.split("&").map((t,r)=>({id:r?null:e.ARTISTID,name:t}))}),l=e=>a("GET",s.kuwoapi?"http://mobi.kuwo.cn/mobi.s?f=kuwo&q="+s.kuwoapi.encryptQuery("corp=kuwo&source=kwplayer_ar_5.1.0.0_B_jiakong_vh.apk&p2p=1&type=convert_url2&sig=0&format="+["flac","mp3"].slice(+!o.ENABLE_FLAC).join("|")+"&rid="+e):"http://antiserver.kuwo.cn/anti.s?type=convert_url&format=mp3&response=url&rid=MUSIC_"+e,{"user-agent":"okhttp/3.10.0"}).then(e=>e.body()).then(e=>(e.match(/http[^\s$"]+/)||[])[0]||Promise.reject()).catch(()=>i().kuwo.track(e)),f=c("provider/kuwo");e.exports={check:e=>f.cache(e,()=>(e=>a("GET","http://search.kuwo.cn/r.s?&correct=1&stype=comprehensive&encoding=utf8&rformat=json&mobi=1&show_copyright_off=1&searchapi=6&all="+encodeURIComponent(e.keyword.replace(" - "," "))).then(e=>e.json()).then(t=>{if(!t||t.content.length<2||!t.content[1].musicpage||t.content[1].musicpage.abslist.length<1)return Promise.reject();let r=o(t.content[1].musicpage.abslist.map(u),e);return r?r.id:Promise.reject()}))(e)).then(l),track:l}},9896:e=>{"use strict";e.exports=require("fs")},9920:(e,t,r)=>{"use strict";var i=r(6518),o=r(6639).forEach;i({target:"AsyncIterator",proto:!0,real:!0,forced:!0},{forEach:function(e){return o(this,e)}})},9929:(e,t,r)=>{"use strict";var i=r(6518),o=r(6080),s=r(6194),a=r(2248),c=r(6223),u=a.Map,l=a.set;i({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=s(this),r=o(e,arguments.length>1?arguments[1]:void 0),i=new u;return c(t,function(e,o){l(i,r(e,o,t),e)}),i}})},9986:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(1393),r(1454);let{cacheStorage:i,CacheStorageGroup:o,getManagedCacheStorage:s}=r(3971),a=r(2569),c=r(9643),u=r(9664),l=r(1600),{logScope:f}=r(5469);f("provider/bilivideo");let p=s("provider/bilivideo"),h=[46,47,18,2,53,8,23,32,15,50,10,31,58,3,45,35,27,43,5,49,33,9,42,19,29,28,14,39,12,38,41,13,37,48,7,16,24,55,40,61,26,17,0,1,60,51,30,4,22,25,54,21,56,59,6,63,57,62,11,36,20,34,44,52];async function d(){let e=await u("GET","https://api.bilibili.com/x/web-interface/nav",{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",Referer:"https://www.bilibili.com/"}),{data:{wbi_img:{img_url:t,sub_url:r}}}=await e.json();return{img_key:t.slice(t.lastIndexOf("/")+1,t.lastIndexOf(".")),sub_key:r.slice(r.lastIndexOf("/")+1,r.lastIndexOf("."))}}let y=async e=>{let{img_key:t,sub_key:r}=await p.cache("wbikey",async()=>await d());return function(e,t,r){let i,o=(i=t+r,h.map(e=>i[e]).join("").slice(0,32)),s=Math.round(Date.now()/1e3),a=/[!'()*]/g;Object.assign(e,{wts:s});let c=Object.keys(e).sort().map(t=>{let r=e[t].toString().replace(a,"");return`${encodeURIComponent(t)}=${encodeURIComponent(r)}`}).join("&"),u=l.md5.digest(c+o);return c+"&w_rid="+u}(e,t,r)},g=e=>({id:e.bvid,name:e.title,artists:{id:e.typeid,name:e.typename}}),m=async()=>p.cache("bilicookie",()=>u("GET","https://www.bilibili.com").then(e=>e.headers["set-cookie"].map(e=>e.split(";")[0]).join("; "))),b=e=>y({bvid:e}).then(t=>u("GET","https://api.bilibili.com/x/web-interface/wbi/view?"+t).then(e=>e.json()).then(t=>0===t.code?y({bvid:e,cid:t.data.cid,fnval:16,platform:"pc"}).then(t=>u("GET","https://api.bilibili.com/x/player/wbi/playurl?"+t).then(e=>e.json()).then(e=>0!==e.code?Promise.reject():null!=e.data.dash.audio?e.data.dash.audio[0].base_url:Promise.reject()).catch(()=>a().bilibili.track(e))):Promise.reject()).catch(()=>a().bilibili.track(e)));e.exports={check:e=>p.cache(e,()=>(e=>m().then(t=>y({search_type:"video",keyword:e.keyword}).then(r=>u("GET","https://api.bilibili.com/x/web-interface/wbi/search/type?"+r,{cookie:t,referer:"https://search.bilibili.com"}).then(e=>e.json()).then(t=>{let r=c(t.data.result.map(g),e);return r?r.id:Promise.reject()}))))(e)).then(b),track:b}}},t={};function r(i){var o=t[i];if(void 0!==o)return o.exports;var s=t[i]={exports:{}};return e[i].call(s.exports,s,s.exports,r),s.exports}(()=>{"use strict";r(1393),r(1454),r(8159),r(7550),r(8992),r(4905),r(8872),r(4114),r(9920),r(3949);let e=r(8330),t=r(2503).program({name:e.name.replace(/@.+\//,""),version:e.version}).option(["-v","--version"],{action:"version"}).option(["-p","--port"],{metavar:"http[:https]",help:"specify server port"}).option(["-a","--address"],{metavar:"address",help:"specify server host"}).option(["-u","--proxy-url"],{metavar:"url",help:"request through upstream proxy"}).option(["-f","--force-host"],{metavar:"host",help:"force the netease server ip"}).option(["-o","--match-order"],{metavar:"source",nargs:"+",help:"set priority of sources"}).option(["-t","--token"],{metavar:"token",help:"set up proxy authentication"}).option(["-e","--endpoint"],{metavar:"url",help:"replace virtual endpoint with public host"}).option(["-s","--strict"],{action:"store_true",help:"enable proxy limitation"}).option(["-c","--cnrelay"],{metavar:"cnrelay",help:"Mainland China relay to get music url"}).option(["-h","--help"],{action:"help"}).parse(process.argv);if(global.address=t.address,t.port=(t.port||"8080:8081").split(":").map(e=>parseInt(e)),t.port.some(e=>isNaN(e)||e<1||e>65535)&&(console.log("Port must be a number higher than 0 and lower than 65535."),process.exit(1)),t.proxyUrl&&!/http(s?):\/\/.+:\d+/.test(t.proxyUrl)&&(console.log("Please check the proxy url."),process.exit(1)),t.endpoint?"-"===t.endpoint?t.endpoint="":/http(s?):\/\/.+/.test(t.endpoint)||(console.log("Please check the endpoint host."),process.exit(1)):t.endpoint="https://music.163.com",t.forceHost&&0===r(9278).isIP(t.forceHost)&&(console.log("Please check the server host."),process.exit(1)),t.matchOrder){let e=Object.keys(r(157).PROVIDERS),i=t.matchOrder;i.some((e,t)=>t!=i.indexOf(e))?(console.log("Please check the duplication in match order."),process.exit(1)):i.some(t=>!e.includes(t))&&(console.log("Please check the availability of match sources."),process.exit(1)),global.source=i}t.token&&!/\S+:\S+/.test(t.token)&&(console.log("Please check the authentication token."),process.exit(1));let{logScope:i}=r(5469),o=r(7016).parse,s=r(8114),a=r(4538),{CacheStorageGroup:c}=r(3971),u=i("app"),l=Array.from(s.target.host);global.port=t.port,global.proxy=t.proxyUrl?o(t.proxyUrl):null,global.hosts=l.reduce((e,r)=>Object.assign(e,{[r]:t.forceHost}),{}),a.whitelist=["://[\\w.]*music\\.126\\.net","://[\\w.]*vod\\.126\\.net","://acstatic-dun.126.net","://[\\w.]*\\.netease.com","://[\\w.]*\\.163yun.com"],global.cnrelay=t.cnrelay,t.strict&&a.blacklist.push(".*"),a.authentication=t.token||null,global.endpoint=t.endpoint,t.endpoint&&a.whitelist.push(escape(t.endpoint));let f="true"===process.env.ENABLE_HTTPDNS?[e=>r(9664)("POST","http://music.httpdns.c.163.com/d",{},e).then(e=>e.json()).then(e=>e.dns.reduce((e,t)=>e.concat(t.ips),[])),e=>r(9664)("GET","http://httpdns.n.netease.com/httpdns/v2/d?domain="+e).then(e=>e.json()).then(e=>Object.keys(e.data).map(t=>e.data[t]).reduce((e,t)=>e.concat(t.ip||[]),[]))]:[],p=c.getInstance();setInterval(()=>{p.cleanup()},9e5),Promise.all(f.map(e=>e(l.join(","))).concat(l.map(e=>new Promise((t,i)=>r(2250).lookup(e,{all:!0},(e,r)=>e?i(e):t(r.map(e=>e.address))))))).then(e=>{let{host:t}=s.target;e.forEach(e=>e.forEach(t.add,t)),a.whitelist=a.whitelist.concat(Array.from(t).map(escape));let r=e=>u.info(`${["HTTP","HTTPS"][e]} Server running @ http://${address||"0.0.0.0"}:${port[e]}`);port[0]&&a.http.listen(port[0],address).once("listening",()=>r(0)),port[1]&&a.https.listen(port[1],address).once("listening",()=>r(1)),cnrelay&&u.info(`CNRelay: ${cnrelay}`)}).catch(e=>{console.log(e),process.exit(1)})})()})();