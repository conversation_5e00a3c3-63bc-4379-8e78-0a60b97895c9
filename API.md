# UnblockNeteaseMusic API 模式

## 🚀 快速开始

### 启动API服务器

```bash
# 方式1：使用npm脚本（推荐）
npm run start:api

# 方式2：使用npm脚本（开发模式，显示详细日志）
npm run start:api-dev

# 方式3：直接命令行启动
node src/app.js -m -p 3000 -a 0.0.0.0 -o qq kugou kuwo migu

# 方式4：自定义端口和配置
node src/app.js -m -p 8080 -a 127.0.0.1 -o qq kugou kuwo migu joox youtube
```

### 服务器启动信息

启动成功后会显示：
```
🚀 HTTP API Server running @ http://0.0.0.0:3000
📖 API 文档:
   GET  http://0.0.0.0:3000/api/health                    - 健康检查
   GET  http://0.0.0.0:3000/api/sources                   - 获取音源列表
   GET  http://0.0.0.0:3000/api/match/:id                 - 单曲匹配
   POST http://0.0.0.0:3000/api/match/batch               - 批量匹配
💡 使用示例:
   curl http://0.0.0.0:3000/api/match/418602084
   curl "http://0.0.0.0:3000/api/match/418602084?sources=qq,kugou"
```

## 📋 API 接口文档

### 1. 健康检查

**请求：**
```http
GET /api/health
```

**响应：**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "0.27.10"
}
```

### 2. 获取音源列表

**请求：**
```http
GET /api/sources
```

**响应：**
```json
{
  "success": true,
  "defaultSources": ["kugou", "kuwo", "migu", "ytdlp"],
  "allSources": ["qq", "kugou", "kuwo", "migu", "joox", "youtube", "youtubedl", "ytdlp", "bilibili", "bilivideo", "pyncmd"]
}
```

### 3. 单曲匹配

**请求：**
```http
GET /api/match/{songId}?sources=qq,kugou,kuwo
```

**参数：**
- `songId` (必需): 网易云音乐歌曲ID
- `sources` (可选): 指定音源，用逗号分隔，不指定则使用默认音源

**成功响应：**
```json
{
  "success": true,
  "data": {
    "id": 418602084,
    "url": "http://music.example.com/song.mp3",
    "size": 4567890,
    "br": 320000,
    "md5": "abc123...",
    "source": "qq"
  }
}
```

**失败响应：**
```json
{
  "success": false,
  "error": "未找到匹配的音源",
  "message": "No match found"
}
```

### 4. 批量匹配

**请求：**
```http
POST /api/match/batch
Content-Type: application/json

{
  "ids": [418602084, 347230, 25906124],
  "sources": ["qq", "kugou", "kuwo"]
}
```

**响应：**
```json
{
  "success": true,
  "results": [
    {
      "id": 418602084,
      "success": true,
      "data": {
        "url": "http://music.example.com/song1.mp3",
        "size": 4567890,
        "br": 320000,
        "md5": "abc123...",
        "source": "qq"
      }
    },
    {
      "id": 347230,
      "success": false,
      "error": "No match found"
    }
  ]
}
```

## 💻 使用示例

### cURL 示例

```bash
# 健康检查
curl http://localhost:3000/api/health

# 获取音源列表
curl http://localhost:3000/api/sources

# 单曲匹配（默认音源）
curl http://localhost:3000/api/match/418602084

# 单曲匹配（指定音源）
curl "http://localhost:3000/api/match/418602084?sources=qq,kugou,kuwo"

# 批量匹配
curl -X POST http://localhost:3000/api/match/batch \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [418602084, 347230, 25906124],
    "sources": ["qq", "kugou", "kuwo"]
  }'
```

### JavaScript 示例

```javascript
// 单曲匹配
async function matchSong(songId, sources = []) {
  const sourcesParam = sources.length ? `?sources=${sources.join(',')}` : '';
  const response = await fetch(`http://localhost:3000/api/match/${songId}${sourcesParam}`);
  const data = await response.json();
  
  if (data.success) {
    console.log('找到音源:', data.data.url);
    console.log('来源:', data.data.source);
    return data.data;
  } else {
    console.error('未找到音源:', data.error);
    throw new Error(data.error);
  }
}

// 批量匹配
async function batchMatch(ids, sources = []) {
  const response = await fetch('http://localhost:3000/api/match/batch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ ids, sources })
  });
  
  const data = await response.json();
  return data.results;
}

// 使用示例
matchSong(418602084, ['qq', 'kugou']).then(result => {
  console.log('匹配结果:', result);
});

batchMatch([418602084, 347230], ['qq', 'kugou', 'kuwo']).then(results => {
  results.forEach(result => {
    if (result.success) {
      console.log(`歌曲 ${result.id} 找到音源: ${result.data.source}`);
    } else {
      console.log(`歌曲 ${result.id} 未找到音源: ${result.error}`);
    }
  });
});
```

### Python 示例

```python
import requests
import json

# 单曲匹配
def match_song(song_id, sources=None):
    url = f"http://localhost:3000/api/match/{song_id}"
    if sources:
        url += f"?sources={','.join(sources)}"
    
    response = requests.get(url)
    data = response.json()
    
    if data['success']:
        print(f"找到音源: {data['data']['url']}")
        print(f"来源: {data['data']['source']}")
        return data['data']
    else:
        print(f"未找到音源: {data['error']}")
        raise Exception(data['error'])

# 批量匹配
def batch_match(ids, sources=None):
    url = "http://localhost:3000/api/match/batch"
    payload = {"ids": ids}
    if sources:
        payload["sources"] = sources
    
    response = requests.post(url, json=payload)
    data = response.json()
    return data['results']

# 使用示例
try:
    result = match_song(418602084, ['qq', 'kugou'])
    print("匹配结果:", result)
except Exception as e:
    print("匹配失败:", e)

results = batch_match([418602084, 347230], ['qq', 'kugou', 'kuwo'])
for result in results:
    if result['success']:
        print(f"歌曲 {result['id']} 找到音源: {result['data']['source']}")
    else:
        print(f"歌曲 {result['id']} 未找到音源: {result['error']}")
```

## ⚙️ 配置选项

API模式支持所有原有的配置选项：

```bash
node src/app.js -m \
  -p 3000 \                    # 端口
  -a 0.0.0.0 \                # 监听地址
  -o qq kugou kuwo migu \     # 音源优先级
  -u http://proxy:1080 \      # 上游代理
  -f ************* \          # 强制主机IP
  -s                          # 严格模式
```

## 🔧 与原有功能的区别

| 功能 | 代理模式 | API模式 |
|------|----------|---------|
| 网易云客户端支持 | ✅ | ❌ |
| HTTP API接口 | ❌ | ✅ |
| PAC自动配置 | ✅ | ✅ |
| 程序化调用 | 复杂 | 简单 |
| 跨域支持 | ❌ | ✅ |
| RESTful接口 | ❌ | ✅ |

## 🚨 注意事项

1. **API模式不支持网易云客户端**：API模式专为程序化调用设计，不能作为网易云音乐客户端的代理
2. **端口配置**：API模式建议只使用HTTP端口（如 `-p 3000`），不需要HTTPS端口
3. **CORS支持**：API模式自动启用CORS，支持跨域请求
4. **音源配置**：通过 `-o` 参数配置的音源优先级会作为默认音源使用
5. **错误处理**：API会返回标准的HTTP状态码和JSON错误信息

## 🔄 模式切换

```bash
# 代理模式（原有功能）
npm run start2

# API模式（新功能）
npm run start:api
```

两种模式可以同时运行在不同端口上，互不干扰。
