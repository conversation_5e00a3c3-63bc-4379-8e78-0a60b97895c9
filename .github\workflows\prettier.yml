name: Run Prettier

on:
    push:
        branches:
            - enhanced
        paths-ignore:
            - 'precompiled/**'
    workflow_dispatch:

jobs:
    run-prettier:
        runs-on: ubuntu-latest
        permissions:
            contents: write
            pull-requests: write

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: yarn

            - name: Install dependencies
              run: yarn

            - name: Run prettier
              run: yarn prettier -w .

            - name: Create Pull Request
              uses: peter-evans/create-pull-request@v7
              with:
                  token: ${{ github.token }}
                  commit-message: 'style: with prettier [skip ci]'
                  author: 'github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>'
                  branch: ci/prettier
                  delete-branch: true
                  title: 'style: with prettier'
                  body: |
                      Please help me check this. Thanks! :-)
                  labels: ci:style
