# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
    # Maintain dependencies for GitHub Actions
    - package-ecosystem: 'github-actions'
      directory: '/'
      schedule:
          interval: 'weekly'

    # Maintain dependencies for npm
    - package-ecosystem: 'npm'
      directory: '/'
      schedule:
          interval: 'weekly'
