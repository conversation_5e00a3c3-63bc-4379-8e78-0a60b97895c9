{"name": "@unblockneteasemusic/server", "version": "0.27.10", "description": "Revive unavailable songs for Netease Cloud Music", "main": "src/provider/match.js", "bin": {"unblockneteasemusic": "./precompiled/app.js"}, "engines": {"node": ">= 12"}, "scripts": {"start:dev": "cross-env LOG_LEVEL=debug node src/app.js -e https://music.163.com", "start": "cross-env node src/app.js -e https://music.163.com", "start2": "cross-env node src/app.js -e https://music.163.com -p 50080:50089 -a 127.0.0.1 --force-host htts://music.163.com", "build": "webpack", "pkg": "pkg . --out-path=dist/", "test": "jest"}, "pkg": {"assets": ["server.key", "server.crt"], "targets": ["node18-linux-arm64", "node18-win-arm64", "node18-linux-x64", "node18-win-x64"], "outputPath": "dist"}, "repository": {"type": "git", "url": "https://github.com/UnblockNeteaseMusic/server.git"}, "author": "nondanee, **********, pan93412", "license": "LGPL-3.0-only", "dependencies": {"node-windows": "^1.0.0-beta.8", "pino": "6.14.0", "pino-pretty": "^7.6.1"}, "devDependencies": {"@swc/core": "^1.13.2", "@types/node": "^24.1.0", "@types/pino": "6.3.12", "@yarnpkg/sdks": "^3.2.2", "browserslist": "^4.25.1", "core-js": "^3.44.0", "cross-env": "^7.0.3", "jest": "^30.0.5", "pkg": "^5.8.1", "prettier": "^3.6.2", "swc-loader": "^0.2.6", "terser-webpack-plugin": "^5.3.14", "typescript": "^5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}, "resolutions": {"cacheable-request": "^10.2.9", "minimatch": "^3.0.8", "minimist": "^1.2.8", "semver": "^7.5.4"}, "publishConfig": {"access": "public"}, "packageManager": "yarn@3.8.7"}