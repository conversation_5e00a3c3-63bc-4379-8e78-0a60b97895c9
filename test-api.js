#!/usr/bin/env node

/**
 * UnblockNeteaseMusic API 测试脚本
 * 用于测试API模式的各个接口功能
 */

const http = require('http');

const API_BASE = 'http://127.0.0.1:3000';

// HTTP请求封装
function request(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const data = JSON.parse(body);
          resolve({ status: res.statusCode, data });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试函数
async function testHealthCheck() {
  console.log('🔍 测试健康检查...');
  try {
    const result = await request('GET', `${API_BASE}/api/health`);
    if (result.status === 200 && result.data.status === 'ok') {
      console.log('✅ 健康检查通过');
      console.log(`   版本: ${result.data.version}`);
      console.log(`   时间: ${result.data.timestamp}`);
    } else {
      console.log('❌ 健康检查失败');
    }
  } catch (error) {
    console.log('❌ 健康检查错误:', error.message);
  }
  console.log('');
}

async function testSources() {
  console.log('🔍 测试音源列表...');
  try {
    const result = await request('GET', `${API_BASE}/api/sources`);
    if (result.status === 200 && result.data.success) {
      console.log('✅ 音源列表获取成功');
      console.log(`   默认音源: ${result.data.defaultSources.join(', ')}`);
      console.log(`   所有音源: ${result.data.allSources.join(', ')}`);
    } else {
      console.log('❌ 音源列表获取失败');
    }
  } catch (error) {
    console.log('❌ 音源列表错误:', error.message);
  }
  console.log('');
}

async function testSingleMatch() {
  console.log('🔍 测试单曲匹配...');
  const songId = 418602084; // 稻香 - 周杰伦
  
  try {
    const result = await request('GET', `${API_BASE}/api/match/${songId}`);
    if (result.status === 200 && result.data.success) {
      console.log('✅ 单曲匹配成功');
      console.log(`   歌曲ID: ${result.data.data.id}`);
      console.log(`   音源: ${result.data.data.source}`);
      console.log(`   音质: ${result.data.data.br}bps`);
      console.log(`   大小: ${Math.round(result.data.data.size / 1024 / 1024 * 100) / 100}MB`);
      console.log(`   URL: ${result.data.data.url.substring(0, 80)}...`);
    } else {
      console.log('❌ 单曲匹配失败');
      console.log(`   错误: ${result.data.error}`);
    }
  } catch (error) {
    console.log('❌ 单曲匹配错误:', error.message);
  }
  console.log('');
}

async function testSingleMatchWithSources() {
  console.log('🔍 测试指定音源的单曲匹配...');
  const songId = 418602084;
  const sources = 'kuwo,kugou';
  
  try {
    const result = await request('GET', `${API_BASE}/api/match/${songId}?sources=${sources}`);
    if (result.status === 200 && result.data.success) {
      console.log('✅ 指定音源匹配成功');
      console.log(`   歌曲ID: ${result.data.data.id}`);
      console.log(`   音源: ${result.data.data.source}`);
      console.log(`   指定音源: ${sources}`);
    } else {
      console.log('❌ 指定音源匹配失败');
      console.log(`   错误: ${result.data.error}`);
    }
  } catch (error) {
    console.log('❌ 指定音源匹配错误:', error.message);
  }
  console.log('');
}

async function testBatchMatch() {
  console.log('🔍 测试批量匹配...');
  const data = {
    ids: [418602084, 347230, 25906124], // 稻香, 夜曲, 七里香
    sources: ['kuwo', 'kugou', 'migu']
  };
  
  try {
    const result = await request('POST', `${API_BASE}/api/match/batch`, data);
    if (result.status === 200 && result.data.success) {
      console.log('✅ 批量匹配完成');
      result.data.results.forEach((item, index) => {
        if (item.success) {
          console.log(`   歌曲${item.id}: ✅ ${item.data.source} (${item.data.br}bps)`);
        } else {
          console.log(`   歌曲${item.id}: ❌ ${item.error}`);
        }
      });
    } else {
      console.log('❌ 批量匹配失败');
    }
  } catch (error) {
    console.log('❌ 批量匹配错误:', error.message);
  }
  console.log('');
}

async function test404() {
  console.log('🔍 测试404错误...');
  try {
    const result = await request('GET', `${API_BASE}/api/nonexistent`);
    if (result.status === 404) {
      console.log('✅ 404错误处理正确');
    } else {
      console.log('❌ 404错误处理异常');
    }
  } catch (error) {
    console.log('❌ 404测试错误:', error.message);
  }
  console.log('');
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试 UnblockNeteaseMusic API');
  console.log('=' .repeat(50));
  console.log('');

  await testHealthCheck();
  await testSources();
  await testSingleMatch();
  await testSingleMatchWithSources();
  await testBatchMatch();
  await test404();

  console.log('🎉 测试完成！');
  console.log('');
  console.log('💡 提示：');
  console.log('   - 如果某些音源匹配失败，可能是网络问题或音源暂时不可用');
  console.log('   - 可以尝试不同的音源组合');
  console.log('   - API服务器需要先启动: npm run start:api');
}

// 检查API服务器是否运行
async function checkServer() {
  try {
    await request('GET', `${API_BASE}/api/health`);
    return true;
  } catch (error) {
    return false;
  }
}

// 启动测试
async function main() {
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ API服务器未运行！');
    console.log('请先启动API服务器: npm run start:api');
    process.exit(1);
  }
  
  await runTests();
}

main().catch(console.error);
