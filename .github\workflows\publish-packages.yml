name: Publish to <PERSON><PERSON> on tagged

on:
    release:
        types:
            - published

    # Allows you to run this workflow manually from the Actions tab
    workflow_dispatch:

jobs:
    publish-to-npm:
        runs-on: ubuntu-latest
        steps:
            # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
            - name: Checkout
              uses: actions/checkout@v4

              # Setup .npmrc file to publish to npm
            - uses: actions/setup-node@v4
              with:
                  node-version: 20

            - run: yarn
            - run: yarn npm publish
              env:
                  NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
                  NODE_PUBLISH_REGISTRY: 'https://registry.npmjs.org'

    publish-to-github:
        runs-on: ubuntu-latest
        permissions:
            contents: read
            packages: write
        steps:
            # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
            - name: Checkout
              uses: actions/checkout@v4

              # Setup .npmrc file to publish to npm
            - uses: actions/setup-node@v4
              with:
                  node-version: 20

            - run: yarn
            - run: yarn npm publish
              env:
                  NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                  NODE_PUBLISH_REGISTRY: 'https://npm.pkg.github.com'
